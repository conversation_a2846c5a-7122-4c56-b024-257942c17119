import cv2
import numpy as np
import time
import logging # 添加导入
from app_state import AppState
from constants import * # 导入 LED_HISTORY_SIZE

def calculate_bright_pixel_average(roi_area, bright_percent=BRIGHT_PIXEL_PERCENT):
    """
    计算ROI中最亮N%像素的平均BGR值

    Args:
        roi_area: ROI区域图像 (H, W, 3)
        bright_percent: 采样最亮像素的百分比 (0.0-1.0)

    Returns:
        avg_color_bgr: 最亮像素的平均BGR值 (3,)
    """
    if not ENABLE_PRECISION_OPT_STAGE1 or bright_percent <= 0:
        # 功能关闭或参数无效时，回退到原始方法
        return np.mean(roi_area, axis=(0, 1), dtype=np.float64)

    if roi_area.size == 0:
        return np.array([0.0, 0.0, 0.0])

    # 展平ROI为像素列表
    roi_flat = roi_area.reshape(-1, 3)

    # 计算每个像素的灰度值
    gray_vals = cv2.cvtColor(roi_area, cv2.COLOR_BGR2GRAY).flatten()

    # ROI太小时回退到原始方法
    if len(gray_vals) < 10:
        return np.mean(roi_area, axis=(0, 1), dtype=np.float64)

    # 计算亮度阈值（最亮N%的下界）
    thresh = np.percentile(gray_vals, 100 * (1 - bright_percent))

    # 创建掩码：选择最亮的N%像素
    mask = gray_vals >= thresh

    # 防止极端情况：最亮像素太少
    if mask.sum() < 3:
        return np.mean(roi_area, axis=(0, 1), dtype=np.float64)

    # 计算最亮像素的平均BGR值
    return roi_flat[mask].mean(axis=0).astype(np.float64)

def led_capture_samples(app_state: AppState, num_frames=LED_SAMPLE_FRAMES):
    """捕捉帧并计算有效 LED ROI 的平均 BGR 值"""
    if not app_state.cap or not app_state.cap.isOpened():
        print("错误：无法采集样本，摄像头未初始化。")
        return None

    valid_led_rois_indices = [i for i, roi in enumerate(app_state.led_rois) if roi and isinstance(roi, tuple) and len(roi) == 4]
    if not valid_led_rois_indices:
        print("错误：无法采样，没有有效的 LED ROI 定义。")
        return None

    # 初始化样本列表，长度为 max_rois
    samples_per_roi = [[] for _ in range(app_state.led_max_rois)]
    frames_captured = 0
    print(f"开始采集 {num_frames} 帧 LED 样本 (为 {len(valid_led_rois_indices)} 个有效 ROI)...")

    capture_start_time = time.time()
    max_capture_duration = 10 # seconds, prevent infinite loop

    while frames_captured < num_frames and (time.time() - capture_start_time < max_capture_duration):
        ret, frame = app_state.cap.read()
        if not ret or frame is None:
            print(f"警告：采集 LED 样本时无法读取帧 (尝试第 {frames_captured+1} 帧)")
            time.sleep(0.1)
            continue

        for i in valid_led_rois_indices:
            x, y, w, h = app_state.led_rois[i]
            if w <= 0 or h <= 0: continue # 跳过无效尺寸

            # 边界检查
            y_end = min(y + h, frame.shape[0])
            x_end = min(x + w, frame.shape[1])
            roi_y = max(0, y)
            roi_x = max(0, x)

            if roi_y >= y_end or roi_x >= x_end: continue # ROI 在帧外

            roi_area = frame[roi_y:y_end, roi_x:x_end]
            if roi_area.size > 0:
                try:
                    # 使用最亮像素采样算法
                    avg_color = calculate_bright_pixel_average(roi_area)
                    if np.isnan(avg_color).any():
                         # print(f"警告: ROI {i} 的平均颜色计算结果为 NaN。") # 减少冗余
                         continue
                    # 存储为 numpy array (float64)
                    samples_per_roi[i].append(avg_color)
                except Exception as e:
                     is_green = i < app_state.led_num_green
                     led_label = f"G{i+1}" if is_green else f"R{i - app_state.led_num_green + 1}"
                     print(f"计算 LED {led_label} ROI 颜色均值时出错: {e}")
            # else: # ROI 面积为 0
                # print(f"Warning: LED ROI {i} area is empty (frame {frames_captured+1}).")

        frames_captured += 1
        # time.sleep(0.05) # 移除短暂休眠，让摄像头自行处理

    if time.time() - capture_start_time >= max_capture_duration:
         print(f"警告：LED 样本采集超时（超过 {max_capture_duration} 秒）。")

    print("LED 样本采集完成。")
    # 检查是否所有 *有效* ROI 都采集到了样本
    all_valid_sampled = True
    for i in valid_led_rois_indices:
        if not samples_per_roi[i]:
            is_green = i < app_state.led_num_green
            led_label = f"G{i+1}" if is_green else f"R{i - app_state.led_num_green + 1}"
            print(f"警告：有效的 LED {led_label} (ROI {i}) 未能采集到任何样本。")
            all_valid_sampled = False

    if not all_valid_sampled:
         print("警告：并非所有定义的 LED ROI 都成功采集了样本。")
         # 返回 None 表示采集不完整？或者返回部分样本？这里选择返回部分样本
         # return None
         pass

    # 返回完整的列表结构
    return samples_per_roi


def led_calculate_thresholds(app_state: AppState):
    """根据 AppState 中的样本计算并更新 LED 阈值"""
    off_samples = app_state.led_off_state_samples
    on_samples = app_state.led_on_state_samples
    num_green = app_state.led_num_green
    num_total = app_state.led_max_rois

    if not isinstance(off_samples, list) or not isinstance(on_samples, list) or \
       len(off_samples) != num_total or len(on_samples) != num_total:
        print("错误：LED 亮/灭样本数据结构无效或长度不匹配，无法计算阈值。")
        # 不修改 AppState 中的现有阈值
        return

    if num_total == 0:
         print("错误：没有配置 LED ROI，无法计算阈值。")
         return

    num_red = num_total - num_green
    if num_red < 0: # 理论上不应发生，因为 AppState 结构应保持一致
         print(f"警告：内部状态错误，总样本数 ({num_total}) 小于绿色 LED 数 ({num_green})。")
         num_red = 0

    # --- 计算绿色 LED 阈值 ---
    avg_gray_off_g, avg_green_off_g = [], []
    avg_gray_on_g, avg_green_on_g = [], []
    valid_green_rois_with_samples = 0
    if num_green > 0:
        for i in range(num_green):
            # 跳过G33特殊LED，它将单独处理
            if i == SPECIAL_LED_G33_INDEX:
                continue

            # 检查样本是否存在且有效
            if not (off_samples[i] and isinstance(off_samples[i], list) and \
                    on_samples[i] and isinstance(on_samples[i], list) and \
                    all(isinstance(s, np.ndarray) and s.shape == (3,) for s in off_samples[i]) and \
                    all(isinstance(s, np.ndarray) and s.shape == (3,) for s in on_samples[i])):
                # print(f"跳过绿色 LED ROI {i+1}，因为样本无效或缺失。") # 减少冗余
                continue

            valid_green_rois_with_samples += 1
            try:
                # 使用 float64 计算平均颜色
                avg_off_bgr = np.mean(np.array(off_samples[i]), axis=0, dtype=np.float64)
                # 转换为 uint8 用于 cvtColor，注意这会丢失精度，但用于灰度计算通常足够
                avg_off_gray = cv2.cvtColor(np.uint8([[avg_off_bgr]]), cv2.COLOR_BGR2GRAY)[0][0]
                avg_off_green = avg_off_bgr[1] # 直接从 float64 获取 G 通道值
                avg_gray_off_g.append(float(avg_off_gray))
                avg_green_off_g.append(float(avg_off_green))

                avg_on_bgr = np.mean(np.array(on_samples[i]), axis=0, dtype=np.float64)
                avg_on_gray = cv2.cvtColor(np.uint8([[avg_on_bgr]]), cv2.COLOR_BGR2GRAY)[0][0]
                avg_on_green = avg_on_bgr[1]
                avg_gray_on_g.append(float(avg_on_gray))
                avg_green_on_g.append(float(avg_on_green))
            except Exception as e:
                 print(f"处理绿色 LED ROI {i+1} 样本时出错: {e}")
                 valid_green_rois_with_samples -= 1

    suggested_gray_g = DEFAULT_LED_GRAY_THRESHOLD_GREEN
    suggested_green = DEFAULT_LED_GREEN_THRESHOLD
    if valid_green_rois_with_samples > 0:
        mean_gray_off = np.mean(avg_gray_off_g)
        mean_green_off = np.mean(avg_green_off_g)
        mean_gray_on = np.mean(avg_gray_on_g)
        mean_green_on = np.mean(avg_green_on_g)
        gray_range = mean_gray_on - mean_gray_off
        green_range = mean_green_on - mean_green_off

        # 策略：阈值设在亮灭均值中间，如果范围太小则靠近亮状态均值
        suggested_gray_g = mean_gray_off + gray_range * 0.5 if gray_range > 5 else mean_gray_on * 0.8 + mean_gray_off * 0.2 + 2
        suggested_green = mean_green_off + green_range * 0.5 if green_range > 5 else mean_green_on * 0.8 + mean_green_off * 0.2 + 2

        # 确保阈值不低于灭灯均值，也不高于亮灯均值（加一点缓冲）
        suggested_gray_g = np.clip(suggested_gray_g, mean_gray_off + 1, mean_gray_on + 10)
        suggested_green = np.clip(suggested_green, mean_green_off + 1, mean_green_on + 10)

        print(f"绿色 LED 建议阈值：灰度={suggested_gray_g:.1f}, 绿色={suggested_green:.1f}")
        # 更新 AppState
        app_state.led_gray_threshold_green = max(0.0, suggested_gray_g)
        app_state.led_green_threshold = max(0.0, suggested_green)
    elif num_green > 0:
        print("没有有效的绿色 LED 样本来计算阈值，将保留或使用默认值。")

    # --- 计算G33特殊LED阈值 ---
    if SPECIAL_LED_G33_INDEX < num_total and SPECIAL_LED_G33_INDEX < len(off_samples) and SPECIAL_LED_G33_INDEX < len(on_samples):
        # 检查G33样本是否存在且有效
        if (off_samples[SPECIAL_LED_G33_INDEX] and isinstance(off_samples[SPECIAL_LED_G33_INDEX], list) and \
            on_samples[SPECIAL_LED_G33_INDEX] and isinstance(on_samples[SPECIAL_LED_G33_INDEX], list) and \
            all(isinstance(s, np.ndarray) and s.shape == (3,) for s in off_samples[SPECIAL_LED_G33_INDEX]) and \
            all(isinstance(s, np.ndarray) and s.shape == (3,) for s in on_samples[SPECIAL_LED_G33_INDEX])):

            try:
                # 计算G33的平均颜色
                avg_off_bgr_g33 = np.mean(np.array(off_samples[SPECIAL_LED_G33_INDEX]), axis=0, dtype=np.float64)
                avg_off_gray_g33 = cv2.cvtColor(np.uint8([[avg_off_bgr_g33]]), cv2.COLOR_BGR2GRAY)[0][0]
                avg_off_green_g33 = avg_off_bgr_g33[1]

                avg_on_bgr_g33 = np.mean(np.array(on_samples[SPECIAL_LED_G33_INDEX]), axis=0, dtype=np.float64)
                avg_on_gray_g33 = cv2.cvtColor(np.uint8([[avg_on_bgr_g33]]), cv2.COLOR_BGR2GRAY)[0][0]
                avg_on_green_g33 = avg_on_bgr_g33[1]

                gray_range_g33 = avg_on_gray_g33 - avg_off_gray_g33
                green_range_g33 = avg_on_green_g33 - avg_off_green_g33

                # 计算G33独立阈值
                suggested_gray_g33 = avg_off_gray_g33 + gray_range_g33 * 0.5 if gray_range_g33 > 5 else avg_on_gray_g33 * 0.8 + avg_off_gray_g33 * 0.2 + 2
                suggested_green_g33 = avg_off_green_g33 + green_range_g33 * 0.5 if green_range_g33 > 5 else avg_on_green_g33 * 0.8 + avg_off_green_g33 * 0.2 + 2

                # 确保阈值合理
                suggested_gray_g33 = np.clip(suggested_gray_g33, avg_off_gray_g33 + 1, avg_on_gray_g33 + 10)
                suggested_green_g33 = np.clip(suggested_green_g33, avg_off_green_g33 + 1, avg_on_green_g33 + 10)

                print(f"G33特殊LED建议阈值：灰度={suggested_gray_g33:.1f}, 绿色={suggested_green_g33:.1f}")
                # 更新G33独立阈值
                app_state.led_gray_threshold_g33 = max(0.0, suggested_gray_g33)
                app_state.led_green_threshold_g33 = max(0.0, suggested_green_g33)

            except Exception as e:
                print(f"处理G33特殊LED样本时出错: {e}")
        else:
            print("G33特殊LED样本无效或缺失，将保留现有阈值。")

    # --- 计算红色 LED 阈值 ---
    avg_gray_off_r, avg_red_off_r = [], []
    avg_gray_on_r, avg_red_on_r = [], []
    valid_red_rois_with_samples = 0
    if num_red > 0:
        for i in range(num_green, num_total):
            if not (off_samples[i] and isinstance(off_samples[i], list) and \
                    on_samples[i] and isinstance(on_samples[i], list) and \
                    all(isinstance(s, np.ndarray) and s.shape == (3,) for s in off_samples[i]) and \
                    all(isinstance(s, np.ndarray) and s.shape == (3,) for s in on_samples[i])):
                # print(f"跳过红色 LED ROI {i - num_green + 1}，因为样本无效或缺失。")
                continue

            valid_red_rois_with_samples += 1
            try:
                avg_off_bgr = np.mean(np.array(off_samples[i]), axis=0, dtype=np.float64)
                avg_off_gray = cv2.cvtColor(np.uint8([[avg_off_bgr]]), cv2.COLOR_BGR2GRAY)[0][0]
                avg_off_red = avg_off_bgr[2] # Red channel
                avg_gray_off_r.append(float(avg_off_gray))
                avg_red_off_r.append(float(avg_off_red))

                avg_on_bgr = np.mean(np.array(on_samples[i]), axis=0, dtype=np.float64)
                avg_on_gray = cv2.cvtColor(np.uint8([[avg_on_bgr]]), cv2.COLOR_BGR2GRAY)[0][0]
                avg_on_red = avg_on_bgr[2] # Red channel
                avg_gray_on_r.append(float(avg_on_gray))
                avg_red_on_r.append(float(avg_on_red))
            except Exception as e:
                 print(f"处理红色 LED ROI {i - num_green + 1} 样本时出错: {e}")
                 valid_red_rois_with_samples -= 1

    suggested_gray_r = DEFAULT_LED_GRAY_THRESHOLD_RED
    suggested_red = DEFAULT_LED_RED_THRESHOLD
    if valid_red_rois_with_samples > 0:
        mean_gray_off = np.mean(avg_gray_off_r)
        mean_red_off = np.mean(avg_red_off_r)
        mean_gray_on = np.mean(avg_gray_on_r)
        mean_red_on = np.mean(avg_red_on_r)
        gray_range = mean_gray_on - mean_gray_off
        red_range = mean_red_on - mean_red_off

        suggested_gray_r = mean_gray_off + gray_range * 0.5 if gray_range > 5 else mean_gray_on * 0.8 + mean_gray_off * 0.2 + 2
        suggested_red = mean_red_off + red_range * 0.5 if red_range > 5 else mean_red_on * 0.8 + mean_red_off * 0.2 + 2

        suggested_gray_r = np.clip(suggested_gray_r, mean_gray_off + 1, mean_gray_on + 10)
        suggested_red = np.clip(suggested_red, mean_red_off + 1, mean_red_on + 10)

        print(f"红色 LED 建议阈值：灰度={suggested_gray_r:.1f}, 红色={suggested_red:.1f}")
        app_state.led_gray_threshold_red = max(0.0, suggested_gray_r)
        app_state.led_red_threshold = max(0.0, suggested_red)
    elif num_red > 0:
        print("没有有效的红色 LED 样本来计算阈值，将保留或使用默认值。")

def detect_led_status(frame: np.ndarray, app_state: AppState):
    """
    分析帧中所有已定义 LED ROI 的状态，并应用多层过滤提高稳定性。
    直接更新 AppState 中的 led_last_status, led_last_values, led_history,
    led_last_stable_status, led_last_change_time。
    """
    # 性能监控开始
    start_time = time.perf_counter() if ENABLE_PRECISION_OPT_STAGE1 else None

    if app_state.led_max_rois <= 0 or frame is None:
        # 如果没有 ROI 或帧，确保状态列表是正确的空/默认值
        app_state.led_last_status = [False] * app_state.led_max_rois
        app_state.led_last_values = [(0.0, 0.0, 0.0)] * app_state.led_max_rois
        # 不需要返回，因为直接修改 app_state
        return

    frame_h, frame_w = frame.shape[:2]
    current_time = time.time()

    # 用于存储当前帧计算结果的临时列表
    current_gray_values = [0.0] * app_state.led_max_rois
    current_green_values = [0.0] * app_state.led_max_rois
    current_red_values = [0.0] * app_state.led_max_rois
    initial_statuses = [False] * app_state.led_max_rois
    roi_validity = [False] * app_state.led_max_rois # 标记 ROI 是否有效

    # --- a. 计算当前帧原始值和初步状态 ---
    for idx, roi in enumerate(app_state.led_rois):
        if not roi or not isinstance(roi, (list, tuple)) or len(roi) != 4:
            continue
        x, y, w, h = roi
        if w <= 0 or h <= 0:
             continue

        y_end = min(y + h, frame_h); x_end = min(x + w, frame_w)
        roi_y = max(0, y); roi_x = max(0, x)
        if roi_y >= y_end or roi_x >= x_end: continue

        roi_area = frame[roi_y:y_end, roi_x:x_end]
        if roi_area.size == 0: continue

        roi_validity[idx] = True # 标记此 ROI 在当前帧有效
        try:
            # 使用最亮像素采样算法
            avg_color_bgr = calculate_bright_pixel_average(roi_area)
            if np.isnan(avg_color_bgr).any():
                 roi_validity[idx] = False; continue

            # 调试日志：对比原始方法和优化方法
            if ENABLE_PRECISION_OPT_STAGE1 and logging.getLogger().isEnabledFor(logging.DEBUG):
                original_avg = np.mean(roi_area, axis=(0, 1), dtype=np.float64)
                is_green_led = (idx < app_state.led_num_green)
                led_label = f"G{idx+1}" if is_green_led else f"R{idx - app_state.led_num_green + 1}"
                logging.debug(f"LED {led_label}: 原始均值={original_avg}, 最亮{BRIGHT_PIXEL_PERCENT*100:.0f}%均值={avg_color_bgr}")

            avg_color_bgr_uint8 = np.uint8([[np.clip(avg_color_bgr, 0, 255)]])
            gray_value = float(cv2.cvtColor(avg_color_bgr_uint8, cv2.COLOR_BGR2GRAY)[0][0])
            blue_value, green_value, red_value = avg_color_bgr # 使用 float64 值

            current_gray_values[idx] = gray_value
            current_green_values[idx] = green_value
            current_red_values[idx] = red_value

            # 获取对应阈值
            is_green_led = (idx < app_state.led_num_green)
            is_special_led_g33 = (idx == SPECIAL_LED_G33_INDEX)  # 直接检查是否为G33

            if is_special_led_g33:
                # G33使用独立阈值
                gray_th = app_state.led_gray_threshold_g33
                color_th = app_state.led_green_threshold_g33
                color_val = green_value
            else:
                # 其他LED使用原有逻辑
                gray_th = app_state.led_gray_threshold_green if is_green_led else app_state.led_gray_threshold_red
                color_th = app_state.led_green_threshold if is_green_led else app_state.led_red_threshold
                color_val = green_value if is_green_led else red_value

            # 双通道初步判断
            initial_statuses[idx] = (gray_value >= gray_th) and (color_val >= color_th)

        except Exception as e:
             roi_validity[idx] = False
             led_label = f"G{idx+1}" if idx < app_state.led_num_green else f"R{idx - app_state.led_num_green + 1}" # Fix label calculation
             print(f"计算 LED {led_label} 颜色时出错: {e}")
             continue

    # --- b. 找出当前帧最亮者 (基于灰度值) ---
    max_gray_value = 0.0
    max_gray_index = -1
    for i in range(app_state.led_max_rois):
        if roi_validity[i] and current_gray_values[i] > max_gray_value:
            max_gray_value = current_gray_values[i]
            max_gray_index = i

    # --- c. 应用初步相对亮度抑制 (仅对绿色LED) ---
    final_initial_statuses = list(initial_statuses) # Start with raw statuses
    if max_gray_index != -1:
        for i in range(app_state.led_max_rois):
            is_green_led = (i < app_state.led_num_green)
            # --- Apply suppression ONLY to GREEN LEDs, but SKIP G33 ---
            if is_green_led and i != SPECIAL_LED_G33_INDEX and roi_validity[i] and initial_statuses[i] and i != max_gray_index:
                brightness_ratio = current_gray_values[i] / max_gray_value if max_gray_value > 0 else 0
                if brightness_ratio < 0.8: # GEMINI.py uses 0.7
                    final_initial_statuses[i] = False # Suppress green only

    # --- d. 更新历史记录 ---
    for i in range(app_state.led_max_rois):
        if roi_validity[i]: # 只为有效 ROI 更新历史
             history = app_state.led_history[i]
             current_values = (current_gray_values[i], current_green_values[i], current_red_values[i])
             is_green_led = (i < app_state.led_num_green)

             # Store status: G33使用原始状态，其他绿色LED使用抑制后状态，红色LED使用原始状态
             if i == SPECIAL_LED_G33_INDEX:
                 status_to_store = initial_statuses[i]  # G33使用原始阈值判断结果
             else:
                 status_to_store = final_initial_statuses[i] if is_green_led else initial_statuses[i]
             history.append((status_to_store, current_values))
             # 保持历史队列大小
             if len(history) > LED_HISTORY_SIZE:
                  history.pop(0)
        # else: 无效 ROI 不更新历史，历史队列可能不是满的

    # --- e. 应用时间平滑 (仅对绿色LED) ---
    smoothed_statuses = [False] * app_state.led_max_rois
    for i in range(app_state.led_max_rois):
        history = app_state.led_history[i]
        is_green_led = (i < app_state.led_num_green)

        if i == SPECIAL_LED_G33_INDEX: # --- G33使用简单的最新状态，不进行时间平滑 ---
            if history: # 直接使用最新状态
                smoothed_statuses[i] = history[-1][0]
            elif roi_validity[i]: # 如果历史为空但ROI有效，使用当前原始状态
                smoothed_statuses[i] = initial_statuses[i]
            # else: 保持 False
        elif is_green_led: # --- Apply smoothing ONLY to other GREEN LEDs ---
            if len(history) == LED_HISTORY_SIZE: # 只有历史队列满时才进行平滑判断
                 # The status stored in history for green LEDs is already the potentially suppressed one
                 on_count = sum(1 for status, _ in history if status)
                 # Apply smoothing rule for green LEDs
                 smoothed_statuses[i] = (on_count >= (LED_HISTORY_SIZE - 1)) # 更严格的判断，例如 5 次里至少 4 次 ON
            elif history: # 如果历史没满但有数据，可以用最新的状态
                 smoothed_statuses[i] = history[-1][0]
            # else: 历史为空，保持 False
        else: # --- For RED LEDs, use the latest raw status ---
             if history: # Use the latest status stored (which was the raw initial status)
                 smoothed_statuses[i] = history[-1][0]
             elif roi_validity[i]: # If history is empty but ROI is valid, use current raw status
                  smoothed_statuses[i] = initial_statuses[i]
             # else: roi not valid, remains False

    # --- f. 应用增强互斥逻辑 (仅对绿色LED) ---
    final_statuses = list(smoothed_statuses) # Start with smoothed/raw statuses
    if max_gray_index != -1: # 同样需要最亮者信息
         # 获取最亮者对应的灰度阈值，用于判断其是否"足够亮"
         brightest_is_green = (max_gray_index < app_state.led_num_green)
         brightest_gray_th = app_state.led_gray_threshold_green if brightest_is_green else app_state.led_gray_threshold_red
         is_brightest_led_truly_bright = (max_gray_value > brightest_gray_th * 1.2) # Threshold factor can be adjusted

         for i in range(app_state.led_max_rois):
              is_green_led = (i < app_state.led_num_green)
              # --- Apply final suppression ONLY to GREEN LEDs, but SKIP G33 ---
              if is_green_led and i != SPECIAL_LED_G33_INDEX and roi_validity[i] and smoothed_statuses[i] and i != max_gray_index:
                  # 使用当前帧的灰度值进行比较
                  brightness_ratio = current_gray_values[i] / max_gray_value if max_gray_value > 0 else 0
                  # Apply suppression rule for green LEDs
                  if brightness_ratio < 0.7 and is_brightest_led_truly_bright: # GEMINI.py uses 0.6
                       final_statuses[i] = False # 强制抑制

    # --- g. 状态变化检测与日志记录 ---
    for i in range(app_state.led_max_rois):
         if not roi_validity[i]: continue # 跳过无效 ROI

         last_stable_status = app_state.led_last_stable_status[i]
         is_green_led = (i < app_state.led_num_green)

         # Determine the status to compare and store based on LED type
         if i == SPECIAL_LED_G33_INDEX:
             current_status_to_check = initial_statuses[i] # G33使用原始阈值判断结果
         elif is_green_led:
             current_status_to_check = final_statuses[i] # Filtered/Suppressed status for other green LEDs
         else:
             # For red, use the raw initial status from the current frame
             current_status_to_check = initial_statuses[i]

         if current_status_to_check != last_stable_status:
             time_since_last_change = current_time - app_state.led_last_change_time[i] if app_state.led_last_change_time[i] > 0 else 0.0
             status_change_text = "OFF->ON" if current_status_to_check else "ON->OFF"

             # 添加亮度信息到日志
             brightness_info = ""
             if max_gray_index != -1:
                  if i == max_gray_index:
                       brightness_info = ", (brightest LED)"
                  else:
                       brightness_ratio = current_gray_values[i] / max_gray_value * 100 if max_gray_value > 0 else 0
                       brightness_info = f", brightness: {brightness_ratio:.1f}% of max"

             is_green_led = (i < app_state.led_num_green)
             led_label = f"G{i+1}" if is_green_led else f"R{i - app_state.led_num_green + 1}"

             log_message = (f"LED {led_label} state {status_change_text}, "
                            f"time since last change: {time_since_last_change*1000:.2f}ms"
                            f"{brightness_info}"
                            f"{' (Filtered)' if is_green_led else ' (Raw Threshold)'}") # Add indicator
             logging.info(log_message) # 使用 logging 记录

             # Update stable status and timestamp using the determined status
             app_state.led_last_stable_status[i] = current_status_to_check
             app_state.led_last_change_time[i] = current_time

    # --- h. 更新 AppState 的最终结果 (用于 UI 显示) ---
    final_ui_statuses = [False] * app_state.led_max_rois
    for i in range(app_state.led_max_rois):
         is_green_led = (i < app_state.led_num_green)

         if i == SPECIAL_LED_G33_INDEX and roi_validity[i]:
              final_ui_statuses[i] = initial_statuses[i] # G33使用原始阈值判断结果
         elif is_green_led:
              final_ui_statuses[i] = final_statuses[i] # Use filtered/suppressed for other green LEDs
         elif roi_validity[i]: # Check validity for red
              final_ui_statuses[i] = initial_statuses[i] # Use raw threshold status for red
         # else: remains False if ROI not valid

    app_state.led_last_status = final_ui_statuses
    # 使用当前帧的原始值更新 led_last_values
    app_state.led_last_values = [(current_gray_values[i], current_green_values[i], current_red_values[i]) for i in range(app_state.led_max_rois)]

    # 性能监控结束
    if start_time is not None:
        elapsed_ms = (time.perf_counter() - start_time) * 1000
        if elapsed_ms > PERFORMANCE_WARNING_THRESHOLD_MS:
            logging.warning(f"LED检测耗时过长: {elapsed_ms:.2f}ms > {PERFORMANCE_WARNING_THRESHOLD_MS}ms")

    # 函数本身不需要返回，因为它直接修改了 app_state
