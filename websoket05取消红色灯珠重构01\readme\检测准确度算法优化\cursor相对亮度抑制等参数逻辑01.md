# 当前led检测使用的算法
_Exported on 2025/8/13 at GMT+8 15:03:35 from Cursor (1.2.2)_

---

**User**

当前的led检测用了那些算法实现

---

**Cursor**

目前的 LED 检测流程在 `led_detector.py` 中完成，核心算法可以分为 6 个阶段（自上而下顺序执行），每个阶段都有明确的目的来提高鲁棒性、降低误报。整体流程如下所示（括号内给出对应的重要函数或代码段）：

1. 最亮像素采样（calculate_bright_pixel_average）  
   • 对每个 ROI 先取灰度图中最亮 N% （常量 `BRIGHT_PIXEL_PERCENT`）的像素，再计算这部分像素的 BGR 均值。  
   • 作用：剔除背景／噪声，只保留 LED 本身的高亮信息，比简单平均更精准。  

2. 双通道阈值一次判定（detect_led_status 步骤 a）  
   • 灰度值 (gray) + 颜色通道值（绿色 G 或红色 R）双阈值同时满足才算亮。  
   • 阈值来自 `AppState`，可由 `led_calculate_thresholds` 根据亮／灭样本自适应生成。  

3. 相对亮度抑制（步骤 c）【仅对绿色 LED 生效】  
   • 找出当前帧灰度最高的 LED，若某绿灯亮度不足最高亮度的 70% 则临时压制为 OFF。  
   • 作用：解决多颗绿灯同时达到阈值但实际只有一颗真正亮起的场景。  

4. 时间平滑／历史投票（步骤 e）【绿色】  
   • 为每颗 LED 维护长度为 `LED_HISTORY_SIZE` 的队列。  
   • 只有当最近 N 帧中亮次数达到 (N-1) 才确认 ON；可有效滤掉偶发抖动。  
   • 红灯直接使用最新一次判定结果，不做时间平滑。  

5. 互斥增强（步骤 f）【绿色】  
   • 再次比较与“当前帧最亮 LED”灰度比；若某绿灯不足 60%，并且最亮者明显“足够亮”，则强制压制。  
   • 进一步避免弱亮产生的误报。  

6. 状态变化检测与日志记录（步骤 g）  
   • 把变化从稳定状态 OFF→ON/ON→OFF 写入 Python logging，附带亮度占比、最亮标记等信息。  

辅助算法 / 机制  
• 动态阈值计算（led_calculate_thresholds）：根据采样的亮／灭样本均值自动给出建议阈值，保证适配不同环境亮度。  
• 多帧样本采集（led_capture_samples）：在限定时间内抓取多帧 ROI，调用最亮像素采样，生成阈值计算所需样本。  
• 性能监控：若单帧检测耗时超过 `PERFORMANCE_WARNING_THRESHOLD_MS`，写入 warning 日志，方便优化。  

总结  
整套检测逻辑综合使用了  
• “最亮 N% 像素”统计学采样  
• 双阈值颜色判别  
• 帧内相对亮度抑制  
• 帧间历史平滑投票  
• 最终互斥强化  
共同协作，既保证响应速度，也显著减少误报、漏报。

---

**User**

现在这些算法都是正常生效 、能够有效运行的嘛？

---

**Cursor**

从代码与默认配置来看，所有关键算法在当前版本里都是“默认开启”并且可以正常执行的，只要：

1. 常量保持默认值（`constants.py`）：  
   • `ENABLE_PRECISION_OPT_STAGE1 = True` → 最亮 N % 像素采样算法生效。  
   • `LED_HISTORY_SIZE = 5` → 时间平滑 / 投票逻辑生效。  
   • 其它抑制阈值、性能阈值等也都已给出合理缺省值。

2. `AppState` 在启动时正确写入：  
   • `led_rois`、`led_num_green`、`led_max_rois` 等必须由校准流程或配置文件初始化；  
   • 亮／灭样本经 `led_capture_samples` 采集后，`led_calculate_thresholds` 会把建议阈值写回 `AppState`，然后 `detect_led_status` 就会使用这些阈值。

3. 运行时注意事项  
   • 若 `BRIGHT_PIXEL_PERCENT` 设为 0 或 `ENABLE_PRECISION_OPT_STAGE1` 设为 `False`，行 19-21 会退回到“简单平均”算法（功能关闭）。  
     ```24:34:led_detector.py
       19|    if not ENABLE_PRECISION_OPT_STAGE1 or bright_percent <= 0:
       20|        return np.mean(roi_area, axis=(0, 1), dtype=np.float64)
     ```  
   • 时间平滑／互斥逻辑只作用于绿色 LED；红灯始终用“单帧双阈值”即可，见行 375-389、403-411。  
   • 如果 ROI 无效或摄像头读取失败，相关 ROI 当帧会被跳过，算法对其它 ROI 仍然生效。  
   • 性能监控在行 468-470，会在帧耗时超过 `PERFORMANCE_WARNING_THRESHOLD_MS`（默认为 2 ms）时打印 `warning`，仅提示优化，不影响功能。  

4. 如何快速验证  
   • 启动程序并打开 `DEBUG` 日志级别，可在控制台看到亮度对比、状态变化等详细信息；  
   • 人为点亮 / 熄灭某个 LED，观察日志中 “LED G1 state OFF->ON” 等字样是否及时出现；  
   • 调低 `LED_HISTORY_SIZE`（例如 1）可临时关闭平滑，用于确认单个步骤的作用。  

因此，只要摄像头正常、ROI/阈值配置正确，整套“最亮像素->双阈值->亮度抑制->时间平滑->互斥增强”流程都会按预期运行；若出现判定异常，优先检查：  
• ROI 是否框到灯体；  
• 阈值是否过低或过高；  
• 常量有没有被改动；  
• 摄像头曝光 / 白平衡设置。

```python
       19|    if not ENABLE_PRECISION_OPT_STAGE1 or bright_percent <= 0:
       20|        return np.mean(roi_area, axis=(0, 1), dtype=np.float64)
```

---

**User**

那相对亮度抑制、互斥增强、最亮 N% 像素 这几个值该如何合理设置呢？

---

**Cursor**

以下三组“关键系数”都是为了在不同环境下取得“足够灵敏但又不过度误报”的平衡。推荐遵循“先测数据→再设阈值→反复验证”的思路，而不是简单照搬固定数字。下面给出可操作的经验范围与调参方法。

────────────────────────────────────────
1. 最亮 N % 像素采样比例（BRIGHT_PIXEL_PERCENT）
────────────────────────────────────────
功能：  
• 把 ROI 按灰度排序，只取最亮 N % 像素计算 BGR 均值，过滤掉背景与噪声。  

经验范围：  
• 小 ROI（< 20×20 px）  → 0.12 ~ 0.20  
• 中 ROI（≈ 30×30 px）  → 0.08 ~ 0.12  
• 大 ROI（≥ 50×50 px）  → 0.05 ~ 0.08  

调参步骤：  
a. 采集熄灯帧，画出 ROI 灰度直方图。  
b. 找到 LED 与背景分界处大约的占比 \(p\_0\)。  
c. 取 BRIGHT_PIXEL_PERCENT ≈ \(p\_0\) ＋ (0.02 ~ 0.05) 做缓冲。  
d. 用日志比较“原始均值”与“最亮 N % 均值”，若后者明显更稳定且差异 < 15 %，说明 N 设得合适。若波动大或出现 NaN，适当调大 N。

────────────────────────────────────────
2. 相对亮度抑制阈值（步骤 c，brightness_ratio < 0.7）
────────────────────────────────────────
功能：  
• 在同一帧只允许“最亮”的绿灯为 ON，次亮绿灯若不足 70 % 亮度则强制 OFF，避免两颗绿灯同时越过双阈值。  

经验范围：0.65 ~ 0.75（建议初始 0.70）  

调参方法：  
① 只点亮 1 颗绿灯拍 50 帧，计算次亮绿灯与最亮绿灯的灰度比 \(\rho_{false}\)。  
② 让两颗绿灯一起点亮，再拍 50 帧，得真实两灯均亮时的灰度比 \(\rho_{true}\)。  
③ 选择阈值 \(T_{rel}\) 满足  
  \(\max(\rho_{false})\) < \(T_{rel}\) < \(\min(\rho_{true})\)。  
 缺数据时，0.70 通常能挡掉误报又保留真亮灯。

────────────────────────────────────────
3. 互斥增强阈值（步骤 f，brightness_ratio < 0.6 & “够亮”系数 1.2）
────────────────────────────────────────
功能：  
• 二次保险。只有当“最亮绿灯”灰度 ≥ (阈值 × 1.2) 时，才会把其他绿灯再用 0.6 阈值压制。  
• 解决“所有灯都稍稍过阈值”但没有明显主灯的极端场景。  

推荐设置：  
• 亮度系数：1.15 ~ 1.25（默认 1.20）  
• 抑制阈值：0.55 ~ 0.65（默认 0.60）  

调参方法与上面类似，只是把前述 \(\rho\) 换成“在全部绿灯都较暗时”的分布。若出现主灯明显但仍误报，降低互斥阈值；若误抑制主灯，适当升高或调低“够亮”系数。

────────────────────────────────────────
4. 时间平滑窗口（LED_HISTORY_SIZE，默认 5）
────────────────────────────────────────
• 窗口大小 5 ≈ 200 ms @25 FPS，能削掉短暂闪烁。  
• 若相机帧率高或灯稳定，可减至 3；  
• 若环境干扰大，可增至 7 ~ 9，但响应延迟也随之上升。

────────────────────────────────────────
实战调参小贴士
────────────────────────────────────────
1. 打开 DEBUG 日志级别，实时观测日志里输出的灰度值 / 比率。  
2. 用脚本让绿灯依次单点亮，再全部亮，记录上述比率；Excel 画箱线图，就能直观看到阈值应该落在哪。  
3. 每改一次常量，重新执行  
 • **熄灯采样 → 亮灯采样 → 自动计算阈值 → 连续检测**  
 确保阈值与算法系数配合良好。  
4. 将硬编码的 0.7、0.6、1.2 抽成常量放进 `constants.py`，后期只改配置文件更安全。  
5. 若环境变化大（白天/夜晚），考虑在 UI 提供滑块或“自学习”模式，动态更新这些阈值。

依照以上方法，你可以快速得到既不过度敏感又能稳准检测的参数组合。

---

**User**

也就是主要的相对亮度抑制阈值如果满足了 就不用互斥增强阈？互斥增强阈只是为了保险？

---

**Cursor**

不是简单的 “满足相对亮度抑制就完全不用互斥增强”，两道阈值各司其职，先后次序如下（仅对绿色 LED 起作用）：

1. 相对亮度抑制  
   • 计算当前帧每颗绿灯灰度与“最亮绿灯”灰度之比 `brightness_ratio`。  
   • 若 `< 0.7`（默认值）则立即把该帧判成 OFF。  
   • 这一步只依据“本帧”数据，对历史状态毫无感知。  

2. 时间平滑 (LED_HISTORY_SIZE)  
   • 把“相对抑制”后的结果写进历史队列；  
   • 用最近 N 帧投票，有可能把某颗灯重新判成 ON（例如前 4 帧都是 ON，刚刚这一帧被抑制，投票结果仍可能是 ON）。  

3. 互斥增强  
   • 基于步骤 2 的“平滑后”结果再做一次检查：  
     - 只有当 a) 该灯此刻仍被认为 ON，且 b) `brightness_ratio < 0.6`（更严格），并且 c) 最亮灯确实“足够亮”（灰度 ≥ 阈值×1.2）时，才强制关掉它。  
   • 目的：  
     - 修正“时间平滑让次亮灯又亮回来”的情况；  
     - 处理主灯特别亮而其它灯只是微弱发光的场景。  

因此：

• 如果第一道 0.7 阈值已经把某灯压制成 OFF，并且历史投票也没有把它抬高，那互斥增强自然不会再触发——它根本进不到 `smoothed_statuses[i] == True` 的分支。  
• 但只要该灯经过历史平滑依然被判为 ON，互斥增强就会用更苛刻的 0.6 + 1.2 系数做“第二保险”。  

总结一句：  
- **相对亮度抑制** 面向“单帧即时误判”；  
- **互斥增强** 面向“历史平滑后的残余误判”和“主灯特别亮”两种极端情况。  
两者互补，而非替代。

---

**User**

那如果第一个灯亮起来了，但是第二个灯因为第一个灯穿光的原因导致检测也是亮起来的，实际只有第一个亮、第二个不是真的亮。 我需要跳高相对亮度抑的值嘛，比如从0.7调制0.85？

---

**Cursor**

可以把相对亮度抑制阈值（0.7）适当提高，但**不要直接跳到 0.85，而是先量化“穿光”带来的亮度比例**，再决定具体数值。理由与步骤如下：

1. 先量化穿光效应  
   1) 只点亮第 1 颗绿灯，保持第 2 颗熄灭，采集 50–100 帧。  
   2) 记录  
      • `gray_1` = 最亮灯（LED1）的灰度  
      • `gray_2` = 被穿光影响的 LED2 灰度  
      • 计算比值 ρ = `gray_2 / gray_1`  
   3) 用 Excel/Matplotlib 画箱线图，找到 **ρ\_max（最大值）**。  
   4) 如果还有“双灯都真的亮”的场景，也采集数据得到 ρ\_true\_min（两灯同时亮时较暗那颗与较亮那颗的比例最小值）。

2. 设定阈值  
   • 推荐让新阈值 \(T_{rel}\) 满足  
     ρ\_max（穿光） < \(T_{rel}\) < ρ\_true\_min（双灯同亮）。  
   • 若暂时没有双灯同亮需求，可取 \(T_{rel}\) = ρ\_max + 0.05 作安全裕量。例如测得 ρ\_max≈0.78，则先调到 0.83。  
   • 典型经验区间：0.75–0.85，再高容易把真正的双灯同亮也压掉。

3. 同步调整互斥增强  
   相对抑制阈值提高后，互斥增强里的二次阈值（0.6）可以保持不变，也可按同样思路顺延至 0.65 左右，保持“大约比第一次低 0.1” 的差距即可。

4. 其他降低穿光误判的思路  
   • **缩小或移动 ROI**：让 ROI 只覆盖 LED 正面最亮区域，减少邻灯光线泄漏。  
   • **调低摄像头曝光**：让真正亮灯与穿光亮度差距变大。  
   • **颜色通道补充**：穿光通常 G、R 通道都偏低，可考虑加入“颜色比值”判定（例如 G/B 比、G 灰度差）。  
   • **物理遮光**：在 PCB 或灯壳间加隔板是最根本的方案。

5. 调参流程建议  
   ```
   调亮度阈值 → 重新采集熄灯/亮灯样本 → led_calculate_thresholds → 连续检测验证
   ```
   每改完阈值都执行一次完整流程，观察日志里 LED2 是否仍被误判、响应延迟是否可接受。

总结：  
• **提高相对亮度抑制阈值是解决穿光误判的直接手段**，但应基于实测数据逐步调高；  
• 先量化穿光比例，再把阈值设在“穿光最大值之上、双灯同亮最小值之下”；  
• 若仍有误判，可继续配合 ROI 调整、互斥增强细调或物理隔光。

```plaintext
   调亮度阈值 → 重新采集熄灯/亮灯样本 → led_calculate_thresholds → 连续检测验证
```

---

**User**

明白了，相对亮度抑制阈值是指当前贞假设亮度为100，值为0.7.如果另一个灯的值大于70，那么就是ON，否则压制为OFF？

---

**Cursor**

可以这样理解，但要注意几个前提与细节：

1. 参考值＝“本帧最亮绿灯的灰度”  
   • 代码里 `brightness_ratio = current_gray_values[i] / max_gray_value`  
   • 因此把最亮绿灯 **当成 100 %**，其它绿灯按灰度占比计算百分数。  

2. 抑制逻辑  
   • 该绿灯 **必须已经通过双阈值**（灰度 + G 通道）才会进入相对亮度比较。  
   • 之后再判断：  
     ```python
     if brightness_ratio < 0.7:
         final_initial_statuses[i] = False   # 压制为 OFF
     ```  
   • 所以结论是：  
     - 亮度 ≥ 70 % → 保持 ON  
     - 亮度 < 70 % → 本帧被压制成 OFF  

3. 作用范围  
   • 仅对绿色 LED 生效，红灯不做相对抑制。  
   • 历史平滑、互斥增强等后续步骤仍会再处理一次，可能让状态变动或被再次压制。

4. 调参时记住  
   • 提高阈值（例如从 0.7 → 0.8）＝要求“次亮灯”必须达到最亮灯的 80 % 才能保留为 ON。  
   • 若两颗绿灯真的同时点亮且亮度接近，80 % 以上通常还能保留；如果灯差距大或存在穿光，则次亮灯会被关掉。

简言之：  
“把最亮绿灯视为 100 %，其它绿灯低于 70 % 就被当帧压制为 OFF，高于 70 % 则暂时保留为 ON”。

```python
     if brightness_ratio < 0.7:
         final_initial_statuses[i] = False   # 压制为 OFF
```

---

**User**

相对亮度抑制阈值、互斥增强阈值、时间平滑窗口 是用灰度值的值 还是单通道值才进行判断啊

---

**Cursor**

结论先说：  
1. 相对亮度抑制阈值（0.7）——用 **灰度值**(gray) 做比值；  
2. 互斥增强阈值（0.6 和 1.2 “够亮”系数）——同样只看 **灰度值**；  
3. 时间平滑窗口（LED_HISTORY_SIZE）——根本不是数值阈值，而是 **帧数长度**，与灰度或颜色都无关。

详细出处与说明  

1. 相对亮度抑制  
```350:357:led_detector.py
brightness_ratio = current_gray_values[i] / max_gray_value
if brightness_ratio < 0.7:
    final_initial_statuses[i] = False
```  
• `current_gray_values` 与 `max_gray_value` 都来自行 318–319 通过 `cv2.cvtColor(..., COLOR_BGR2GRAY)` 计算的 **灰度**。  
• 绿色／红色通道值（current_green_values、current_red_values）只用于最初“双阈值”判定，不参与亮度比。

2. 互斥增强  
```407:410:led_detector.py
brightness_ratio = current_gray_values[i] / max_gray_value
if brightness_ratio < 0.6 and is_brightest_led_truly_bright:
    final_statuses[i] = False
```  
• 同样全用灰度；`is_brightest_led_truly_bright` 中的比较 `max_gray_value > brightest_gray_th * 1.2` 也都是灰度。

3. 时间平滑窗口  
```379:385:led_detector.py
on_count = sum(1 for status, _ in history if status)
smoothed_statuses[i] = (on_count >= (LED_HISTORY_SIZE - 1))
```  
• 窗口大小 LED_HISTORY_SIZE（默认 5）仅表示“最近多少帧参与投票”，不直接涉及任何像素值。  
• 存进 history 的 tuple 为 `(status_to_store, current_values)`，其中 `current_values` 里才包含 `(gray, green, red)`，但平滑逻辑只统计 `status` 布尔值。

因此：  
• 两个“亮度比阈值”(0.7, 0.6) 的比较量都是 **同一帧内各绿灯灰度值的比例**；  
• 颜色通道只在最初的双阈值（灰度阈值 + 颜色阈值）里使用；  
• LED_HISTORY_SIZE 只是“时间窗口长度”，与灰度或颜色无关。

```python
brightness_ratio = current_gray_values[i] / max_gray_value
if brightness_ratio < 0.7:
    final_initial_statuses[i] = False
```

```python
brightness_ratio = current_gray_values[i] / max_gray_value
if brightness_ratio < 0.6 and is_brightest_led_truly_bright:
    final_statuses[i] = False
```

```python
on_count = sum(1 for status, _ in history if status)
smoothed_statuses[i] = (on_count >= (LED_HISTORY_SIZE - 1))
```

---

**User**

那如果我调试阈值的时候，把灰度值设置为大于255、这些算法就不生效了？

---

**Cursor**

是的，如果你把 **灰度阈值**（`led_gray_threshold_green` / `led_gray_threshold_red`）或者 **颜色阈值** 设成 **大于 255** 的数值，等于告诉程序：

“只有灰度（或颜色通道）> 255 才算亮。”

而图像经过 `np.clip(..., 0, 255)` 再转换成 `uint8`，所有像素的灰度/颜色最大只能到 255，不可能更高。结果就是：

1. 双阈值第一次判定  
   ```python
   is_on = (gray_value >= gray_th) and (color_val >= color_th)
   ```  
   • `gray_value` ≤ 255，`gray_th` > 255 ⇒ 条件恒为 **False**。  
   • 所有 `initial_statuses[i]` 都变成 `False`，LED 被判为 OFF。  

2. 相对亮度抑制 / 互斥增强  
   • 这两步只对 **已经被认为 ON** 的绿灯再做压制。  
   • 由于第一步全部为 OFF，它们几乎没有对象可处理，看起来就像“失效”了一样。  

3. 时间平滑窗口  
   • 存进去的 `status` 全是 False，投票也只能得出 False。

4. 日志与 UI  
   • 整个系统会显示所有 LED 常灭；只有摄像头读帧、计算均值、灰度转换这些计算仍在运行。

结论  
• **算法代码仍会执行，但逻辑效果等于“全部关闭检测”**。  
• 灰度、颜色阈值应该保持在 0 – 255 范围内（通常 < 240），否则 LED 永远检测不到亮。  
• 如果想“暂时禁用”某颗 LED 的检测，可把它的 ROI 设置为 `None` 或尺寸 0，而不是把阈值调出合法范围。

```python
   is_on = (gray_value >= gray_th) and (color_val >= color_th)
```

---

**User**

明白了，那其实我实际的需求是每一次检测我都只有1个灯珠亮才是好的，不会存在同时亮2个或者多个的情况，只有穿光影响或者因为短路导致2个同时被点亮，这种我是不是就可以直接调高相对亮度抑制 / 互斥增强的值，提高准确度？

---

**Cursor**

是的——既然**业务上规定任意时刻只允许 1 颗 LED 真实点亮**，那就可以把“赢家通吃”的策略做得更激进，从而进一步削弱穿光或意外短路造成的误报。常见做法有两条路径，你可以按需要二选一，甚至组合使用：

──────────────────────────
路径 A：继续沿用现有逻辑，只把阈值调得更高  
──────────────────────────
1. 初步相对亮度抑制  
   • 把阈值从 0.7 提高到 0.9~0.97（你已临时改成 0.94，方向正确）。  
   • 建议先测“穿光最大比值”再 +0.03~0.05，避免把主灯偶尔的微弱波动误杀。

2. 互斥增强  
   • 同步把 0.6 提高到 0.85 左右，并把“够亮”系数 1.2 调到 1.1~1.15。  
   • 保证两道关卡依旧保持 0.05~0.1 的间隙：  
     ```
     相对抑制阈值      0.94
     互斥增强阈值      0.85
     ```

3. 时间平滑  
   • LED_HISTORY_SIZE 可保持 5；若阈值升得很高导致偶尔被误压，可增到 7，稳住输出。

优点：  
• 改动最小，只需调常量；  
• 对代码依赖最少，容易回退。  

风险：  
• 如果极端情况下主灯亮度很低（灰度只有 110 左右），而穿光比值又接近 0.9，仍可能误报，需要实测确认。

──────────────────────────
路径 B：直接做“Winner-Takes-All”  
──────────────────────────
思路：每帧找出灰度最大的 ROI，只要它通过双阈值，就把其它全部判 OFF；无需比值阈值。

伪码（放在步骤 c 之前即可）  
```python
# 仅示意
if max_gray_index != -1 and initial_statuses[max_gray_index]:
    for i in range(app_state.led_max_rois):
        if i != max_gray_index:
            final_initial_statuses[i] = False
```

优点：  
• 逻辑简单、无阈值调试负担；  
• 永远只会输出 0 或 1 颗 ON，完全符合需求。

缺点：  
• 对“主灯亮度极低”或“主灯未过阈值”时，需要小心避免误把所有灯都关掉；  
• 如果未来确实会出现“双灯同时亮”的新需求，要恢复原逻辑会略麻烦。

──────────────────────────
调参与验证建议  
──────────────────────────
1. 先把 A 路径阈值调到 0.94/0.85，跑一段生产数据；若仍偶发双灯误亮，再升到 0.97/0.90。  
2. 若阈值升到 0.97 仍挡不住穿光，则考虑直接切到路径 B。  
3. 调整后务必重新执行  
   • 熄灯采样 → 亮灯采样 → led_calculate_thresholds → 连续检测  
   确保阈值与算法匹配。  
4. 打开 DEBUG 日志级别，观察“brightness_ratio”及状态变化，确认只有最亮灯保持 ON。

总结  
• **提高相对亮度抑制 / 互斥增强阈值**是当前代码的最小改动方案，先行验证效果即可；  
• 若想一劳永逸且未来不会出现多灯同亮场景，可改为“Winner-Takes-All”绝对互斥逻辑。

```plaintext
     相对抑制阈值      0.94
     互斥增强阈值      0.85
```

```python
# 仅示意
if max_gray_index != -1 and initial_statuses[max_gray_index]:
    for i in range(app_state.led_max_rois):
        if i != max_gray_index:
            final_initial_statuses[i] = False
```

