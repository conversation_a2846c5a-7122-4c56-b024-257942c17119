import asyncio
import websockets
import json
import threading
import time
from datetime import datetime
import queue

class WebSocketServerV2:
    def __init__(self, port=8765):
        self.port = port
        self.clients = set()
        self.running = False
        self.server = None
        self.loop = None
        self.message_queue = queue.Queue()
        self.current_state = {
            'state': 'IDLE',
            'countdown': 0,
            'total_duration': 0,
            'progress': 0,
            'start_time': '',
            'message': '等待检测...',
            'is_active': False
        }
        
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        self.clients.add(websocket)
        print(f"✓ 浏览器客户端已连接，当前连接数: {len(self.clients)}")
        
        # 发送连接确认消息
        await self.send_message(websocket, {
            "type": "connection_confirmed",
            "data": {
                "message": "本地视觉检测程序已连接",
                "version": "2.0.0",
                "current_state": self.current_state
            }
        })
        
        try:
            # 监听客户端消息
            async for message in websocket:
                await self.handle_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            pass
        except Exception as e:
            print(f"客户端连接异常: {e}")
        finally:
            if websocket in self.clients:
                self.clients.remove(websocket)
            print(f"✗ 客户端断开连接，当前连接数: {len(self.clients)}")
    
    async def handle_message(self, websocket, message):
        """处理客户端消息"""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'query_status':
                # 响应状态查询
                await self.send_message(websocket, {
                    "type": "status_response",
                    "data": self.current_state.copy()
                })
                print(f"📤 响应状态查询: {self.current_state['state']}")
                
        except json.JSONDecodeError:
            print(f"❌ 收到无效JSON消息: {message}")
        except Exception as e:
            print(f"❌ 处理客户端消息失败: {e}")
    
    async def send_message(self, websocket, message):
        """发送消息给指定客户端"""
        try:
            await websocket.send(json.dumps(message))
        except websockets.exceptions.ConnectionClosed:
            if websocket in self.clients:
                self.clients.remove(websocket)
        except Exception as e:
            print(f"发送消息失败: {e}")
    
    async def broadcast_message(self, message):
        """广播消息给所有连接的客户端"""
        if not self.clients:
            return
            
        disconnected = set()
        for client in self.clients.copy():
            try:
                await client.send(json.dumps(message))
            except websockets.exceptions.ConnectionClosed:
                disconnected.add(client)
            except Exception as e:
                print(f"广播消息失败: {e}")
                disconnected.add(client)
        
        # 清理断开的连接
        self.clients -= disconnected
    
    async def message_processor(self):
        """处理消息队列"""
        while self.running:
            try:
                # 从队列中获取消息
                message = self.message_queue.get(timeout=1)
                await self.broadcast_message(message)
                self.message_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                print(f"处理消息队列失败: {e}")
    
    def update_state(self, state, countdown=0, total_duration=0, message="", is_active=False):
        """更新当前状态并广播"""
        progress = 0
        if total_duration > 0:
            progress = int((total_duration - countdown) / total_duration * 100)
        
        self.current_state = {
            'state': state,
            'countdown': int(countdown),
            'total_duration': int(total_duration),
            'progress': progress,
            'start_time': datetime.now().strftime("%H:%M:%S"),
            'message': message,
            'is_active': is_active
        }
        
        # 创建广播消息
        broadcast_message = {
            "type": "countdown_update",
            "data": self.current_state.copy()
        }
        
        # 将消息放入队列
        if self.running:
            try:
                self.message_queue.put(broadcast_message, timeout=1)
            except queue.Full:
                print("消息队列已满，跳过此次广播")
    
    async def run_server(self):
        """运行WebSocket服务器"""
        try:
            # 启动服务器
            self.server = await websockets.serve(
                self.handle_client,
                "127.0.0.1",
                self.port
            )
            self.running = True
            print(f"✓ WebSocket服务器已启动: ws://127.0.0.1:{self.port}")
            
            # 启动消息处理器
            processor_task = asyncio.create_task(self.message_processor())
            
            # 等待服务器关闭
            await self.server.wait_closed()
            
            # 取消消息处理器
            processor_task.cancel()
            
        except Exception as e:
            print(f"✗ WebSocket服务器运行失败: {e}")
            self.running = False
    
    def start_server(self):
        """启动WebSocket服务器"""
        def run_in_thread():
            # 创建新的事件循环
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            
            try:
                # 运行服务器
                self.loop.run_until_complete(self.run_server())
            except Exception as e:
                print(f"✗ WebSocket服务器线程失败: {e}")
            finally:
                self.loop.close()
        
        # 在单独线程中运行服务器
        server_thread = threading.Thread(target=run_in_thread, daemon=True)
        server_thread.start()
        
        # 等待服务器启动
        time.sleep(2)
        return server_thread
    
    def stop_server(self):
        """停止WebSocket服务器"""
        self.running = False
        if self.server:
            self.server.close()

# 全局WebSocket服务器实例
websocket_server = WebSocketServerV2()
