#!/usr/bin/env python3
"""
G33特殊LED功能测试脚本
测试G33独立阈值和简化检测逻辑是否正常工作
"""

import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app_state import AppState
from constants import *
import led_detector
import config_manager

def test_g33_constants():
    """测试G33相关常量是否正确定义"""
    print("=== 测试G33常量定义 ===")
    
    # 检查常量是否存在
    assert hasattr(sys.modules['constants'], 'SPECIAL_LED_G33_INDEX'), "SPECIAL_LED_G33_INDEX 常量未定义"
    assert hasattr(sys.modules['constants'], 'SPECIAL_LED_INDICES'), "SPECIAL_LED_INDICES 常量未定义"
    assert hasattr(sys.modules['constants'], 'DEFAULT_LED_GRAY_THRESHOLD_G33'), "DEFAULT_LED_GRAY_THRESHOLD_G33 常量未定义"
    assert hasattr(sys.modules['constants'], 'DEFAULT_LED_GREEN_THRESHOLD_G33'), "DEFAULT_LED_GREEN_THRESHOLD_G33 常量未定义"
    
    # 检查常量值
    assert SPECIAL_LED_G33_INDEX == 32, f"G33索引应为32，实际为{SPECIAL_LED_G33_INDEX}"
    assert 32 in SPECIAL_LED_INDICES, f"G33索引32应在特殊LED列表中，实际列表为{SPECIAL_LED_INDICES}"
    
    print("✓ G33常量定义正确")

def test_app_state_g33_attributes():
    """测试AppState中G33相关属性是否正确初始化"""
    print("=== 测试AppState G33属性 ===")
    
    app_state = AppState()
    
    # 检查G33属性是否存在
    assert hasattr(app_state, 'special_led_indices'), "AppState缺少special_led_indices属性"
    assert hasattr(app_state, 'led_gray_threshold_g33'), "AppState缺少led_gray_threshold_g33属性"
    assert hasattr(app_state, 'led_green_threshold_g33'), "AppState缺少led_green_threshold_g33属性"
    
    # 检查初始值
    assert app_state.special_led_indices == [32], f"特殊LED索引应为[32]，实际为{app_state.special_led_indices}"
    assert app_state.led_gray_threshold_g33 == DEFAULT_LED_GRAY_THRESHOLD_G33, f"G33灰度阈值初始值错误"
    assert app_state.led_green_threshold_g33 == DEFAULT_LED_GREEN_THRESHOLD_G33, f"G33绿色阈值初始值错误"
    
    print("✓ AppState G33属性初始化正确")

def test_g33_threshold_logic():
    """测试G33在LED检测中的阈值逻辑"""
    print("=== 测试G33阈值逻辑 ===")
    
    app_state = AppState()
    # 设置为35个LED（33绿+2红）
    app_state.update_led_counts(33, 2)
    
    # 设置G33独立阈值
    app_state.led_gray_threshold_g33 = 150.0
    app_state.led_green_threshold_g33 = 170.0
    
    # 创建模拟帧数据
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 为G33设置ROI
    g33_index = 32
    app_state.led_rois[g33_index] = (100, 100, 20, 20)
    
    # 在G33 ROI区域设置一些像素值来模拟LED
    frame[100:120, 100:120] = [50, 180, 50]  # BGR格式，绿色通道较高
    
    # 调用检测函数
    led_detector.detect_led_status(frame, app_state)
    
    # 检查G33是否被正确检测
    g33_status = app_state.led_last_status[g33_index]
    g33_values = app_state.led_last_values[g33_index]
    
    print(f"G33状态: {g33_status}")
    print(f"G33值: 灰度={g33_values[0]:.1f}, 绿色={g33_values[1]:.1f}, 红色={g33_values[2]:.1f}")
    print(f"G33阈值: 灰度={app_state.led_gray_threshold_g33:.1f}, 绿色={app_state.led_green_threshold_g33:.1f}")
    
    print("✓ G33阈值逻辑测试完成")

def test_config_save_load():
    """测试G33阈值的配置保存和加载"""
    print("=== 测试G33配置保存加载 ===")
    
    app_state = AppState()
    app_state.update_led_counts(33, 2)
    
    # 设置G33独立阈值
    original_gray = 155.5
    original_green = 175.5
    app_state.led_gray_threshold_g33 = original_gray
    app_state.led_green_threshold_g33 = original_green
    
    # 保存配置
    config_manager.save_config(app_state)
    
    # 重新创建AppState并加载配置
    new_app_state = AppState()
    config_manager.load_config(new_app_state)
    
    # 检查G33阈值是否正确加载
    assert abs(new_app_state.led_gray_threshold_g33 - original_gray) < 0.1, f"G33灰度阈值加载错误: 期望{original_gray}, 实际{new_app_state.led_gray_threshold_g33}"
    assert abs(new_app_state.led_green_threshold_g33 - original_green) < 0.1, f"G33绿色阈值加载错误: 期望{original_green}, 实际{new_app_state.led_green_threshold_g33}"
    
    print("✓ G33配置保存加载正确")

def main():
    """运行所有测试"""
    print("开始G33特殊LED功能测试...\n")
    
    try:
        test_g33_constants()
        test_app_state_g33_attributes()
        test_g33_threshold_logic()
        test_config_save_load()
        
        print("\n🎉 所有测试通过！G33特殊LED功能实现正确。")
        print("\nG33特殊LED功能特点:")
        print("- 使用独立的灰度和绿色通道阈值")
        print("- 跳过相对亮度抑制算法")
        print("- 跳过时间平滑算法")
        print("- 跳过互斥逻辑算法")
        print("- 支持独立的阈值调整和配置保存")
        print("\nUI控制按键:")
        print("- 3/#: 调整G33灰度阈值")
        print("- e/E: 调整G33绿色通道阈值")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
