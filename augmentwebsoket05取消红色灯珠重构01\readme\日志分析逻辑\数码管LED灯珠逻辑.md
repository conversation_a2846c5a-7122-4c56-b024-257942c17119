# 数码管LED灯珠检测逻辑完整说明

## 概述

本文档详细说明35点位LED检测系统的完整逻辑规则，包括ROI1-ROI32的配对模式分析和G33、R1、R2三个特殊LED的独立判断逻辑。

## 系统架构

### LED分布
- **ROI 1-32**：数码管显示LED（绿色），用于配对模式分析
- **ROI 33 (G33)**：特殊绿色LED，独立判断
- **ROI 34 (R1)**：特殊红色LED 1，独立判断  
- **ROI 35 (R2)**：特殊红色LED 2，独立判断

### 双重判断机制
1. **配对模式分析**：ROI1-32的顺序点亮周期检测
2. **特殊LED分析**：G33、R1、R2的ON时间统计

## 第一部分：ROI1-32配对模式分析

### 基本概念

#### 配对关系
ROI1-32按照特定规律组成16对配对LED：
- 配对1：ROI 17 + ROI 1
- 配对2：ROI 18 + ROI 2  
- 配对3：ROI 19 + ROI 3
- ...
- 配对16：ROI 32 + ROI 16

#### 序列编号
- **序列1**：ROI 17, 1, 18, 2, 19, 3, 20, 4
- **序列2**：ROI 21, 5, 22, 6, 23, 7, 24, 8
- **序列3**：ROI 25, 9, 26, 10, 27, 11, 28, 12
- **序列4**：ROI 29, 13, 30, 14, 31, 15, 32, 16

### 完美周期定义

#### 完美周期的条件
1. **必须从序列1开始**：第一个点亮的LED必须是ROI 17
2. **严格按序列顺序**：序列1 → 序列2 → 序列3 → 序列4
3. **配对验证通过**：每个ROI点亮时，其配对ROI必须在合理时间窗口内也点亮
4. **时间窗口要求**：配对LED之间的点亮时间差 ≤ 2000ms
5. **完整性要求**：所有4个序列都必须完成

#### 配对验证逻辑
当ROI X点亮时：
1. 找到其配对ROI Y
2. 检查在时间窗口内ROI Y是否也点亮
3. 验证ROI Y的状态记录确认其确实为ON状态
4. 如果验证失败，记录错误并终止当前周期

#### 序列验证逻辑
1. **序列起始验证**：每个周期必须从序列1的ROI 17开始
2. **序列连续性**：序列1完成后才能进入序列2，依此类推
3. **序列内顺序**：每个序列内的LED必须按规定顺序点亮
4. **跨序列跳跃检测**：如果检测到跳序列（如从序列1直接到序列3），标记为失败

### 判断标准

#### 完美周期计数
- **统计规则**：只有完全符合上述所有条件的周期才被计为"完美周期"
- **容错机制**：任何一个验证失败都会导致当前周期被丢弃，重新开始计数

#### 好坏判断
- **好（GOOD）**：完美周期数 > 0
- **坏（BAD）**：完美周期数 = 0

#### 通信输出
- **M10地址发送值**：
  - 完美周期数 = 0 → 发送值 = 3
  - 完美周期数 > 0 → 发送值 = 1

## 第二部分：特殊LED分析（G33、R1、R2）

### 统一判断标准
所有特殊LED采用相同的时间阈值：
- **好（GOOD）**：总ON时间 > 1.0秒
- **坏（BAD）**：总ON时间 ≤ 1.0秒

### G33（绿色特殊LED）

#### 特征描述
- 位置：ROI 33
- 类型：绿色LED
- 行为：有明显的ON/OFF状态变化

#### 分析逻辑
1. **状态变化检测**：监控日志中的状态变化事件
2. **时间段计算**：
   - 每次OFF→ON开始一个新的ON时间段
   - 每次ON→OFF结束当前ON时间段
   - 累加所有ON时间段的持续时间
3. **边界处理**：如果日志结束时LED仍为ON状态，计算到日志最后时刻的时间
4. **总时间统计**：所有ON时间段的累加和

#### 典型行为模式
- 短暂点亮：0.5秒左右的短时间ON
- 长时间点亮：30+秒的长时间ON
- 总计时间通常在35-40秒范围

### R1（红色LED 1）

#### 特征描述
- 位置：ROI 34（内部编号1）
- 类型：红色LED
- 行为：持续ON状态，极少状态变化

#### 分析逻辑
1. **状态变化检测**：首先尝试查找状态变化事件
2. **持续ON检测**：
   - 如果无状态变化事件，搜索所有状态记录
   - 验证所有记录是否都显示ON状态
   - 计算从第一条记录到最后一条记录的时间跨度
3. **时间计算**：整个检测期间的持续时间
4. **容错处理**：如果有少量状态变化，按G33逻辑处理

#### 典型行为模式
- 检测开始时即为ON状态
- 整个检测过程保持ON状态
- 检测结束时仍为ON状态
- 总时间约等于整个检测时长（35-40秒）

### R2（红色LED 2）

#### 特征描述
- 位置：ROI 35（内部编号2）
- 类型：红色LED
- 行为：有多次ON/OFF状态变化

#### 分析逻辑
1. **状态变化检测**：监控多次ON/OFF切换事件
2. **多时间段计算**：
   - 记录每次OFF→ON到ON→OFF的时间段
   - 累加所有独立的ON时间段
3. **周期性分析**：通常有多个ON/OFF周期
4. **总时间统计**：所有ON时间段的累加和

#### 典型行为模式
- 多次ON/OFF切换（通常3-5次）
- 每次ON持续时间不等（几秒到十几秒）
- 总ON时间在25-35秒范围

### 特殊LED综合判断

#### 综合状态计算
- **GOOD条件**：G33 AND R1 AND R2 都为GOOD
- **BAD条件**：任何一个LED为BAD

#### 通信输出
- **M13地址发送值**：
  - 综合状态 = GOOD → 发送值 = 1
  - 综合状态 = BAD → 发送值 = 3

## 第三部分：系统整体逻辑

### 双重验证机制

#### 独立判断
1. **配对模式分析**：ROI1-32的完美周期统计
2. **特殊LED分析**：G33、R1、R2的ON时间统计

#### 最终输出
系统产生两个独立的判断结果：
- **M10地址**：基于ROI1-32配对模式的完美周期数
- **M13地址**：基于G33、R1、R2的综合ON时间

### 容错和异常处理

#### 时间戳异常
- 无法解析的时间戳：跳过该记录
- 时间倒流：记录警告但继续处理
- 时间差异常：设置合理的上下限

#### 状态记录异常
- 缺失状态信息：跳过该记录
- 状态格式错误：尝试容错解析
- 连续相同状态：正常处理

#### 配对验证异常
- 配对LED未找到：标记配对失败
- 时间窗口超时：标记配对失败
- 状态不一致：标记配对失败

### 性能和可靠性

#### 处理效率
- 单次分析时间：< 0.1秒
- 内存占用：< 50MB
- 支持日志大小：> 10MB

#### 准确性保证
- 严格的时间窗口验证
- 多重状态确认机制
- 完整的错误日志记录

## 总结

本系统通过双重验证机制确保LED检测的准确性和可靠性：

1. **ROI1-32配对模式**：验证数码管显示的完整性和正确性
2. **特殊LED监控**：确保关键指示灯的正常工作状态

两套逻辑相互独立，分别输出到不同的通信地址，为上位机提供全面的LED状态信息。
