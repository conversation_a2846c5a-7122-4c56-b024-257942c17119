@echo off
chcp 65001 >nul
echo ========================================
echo    WebSocket连接问题完整诊断和修复
echo ========================================
echo.

echo [1/6] 检查端口占用情况...
echo 检查端口8765:
netstat -an | findstr :8765
echo 检查端口8766:
netstat -an | findstr :8766
echo.

echo [2/6] 停止所有Python进程...
taskkill /f /im python.exe 2>nul
timeout /t 2 /nobreak >nul

echo [3/6] 重新启动本地代理服务器...
start "本地代理服务器" cmd /k "cd /d %~dp0services && python user_local_proxy20250715last.py"
timeout /t 3 /nobreak >nul

echo [4/6] 重新启动视觉检测程序(新端口8766)...
start "视觉检测程序-8766端口" cmd /k "cd /d %~dp0augmentwebsoket05取消红色灯珠重构01 && python main.py"
timeout /t 5 /nobreak >nul

echo [5/6] 测试WebSocket连接...
cd /d "%~dp0augmentwebsoket05取消红色灯珠重构01"
python test_websocket_v2.py client
timeout /t 3 /nobreak >nul

echo [6/6] 启动Flask Web服务器...
start "Flask Web服务器" cmd /k "cd /d %~dp0 && python app.py"
timeout /t 3 /nobreak >nul

echo.
echo ✓ 所有服务已重新启动！
echo.
echo 现在请：
echo 1. 打开 websocket连接测试.html 测试连接
echo 2. 清除浏览器缓存并刷新网页
echo 3. 检查浏览器控制台是否还有错误
echo.
echo 如果仍有问题，请检查：
echo - 防火墙设置
echo - 杀毒软件是否阻止
echo - 网络代理设置
echo.
pause
