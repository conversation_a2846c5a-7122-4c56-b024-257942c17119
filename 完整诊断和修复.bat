@echo off
chcp 65001 >nul
echo ========================================
echo    WebSocket连接问题完整诊断和修复
echo ========================================
echo.

echo [1/6] 检查端口占用情况...
echo 检查端口8765:
netstat -an | findstr :8765
echo 检查端口8766:
netstat -an | findstr :8766
echo.

echo [2/6] 停止所有Python进程...
taskkill /f /im python.exe 2>nul
timeout /t 2 /nobreak >nul

echo [3/6] 重新启动本地代理服务器...
start "本地代理服务器" cmd /k "cd /d %~dp0services && python user_local_proxy20250715last.py"
timeout /t 3 /nobreak >nul

echo [4/6] 重新启动视觉检测程序(新端口8766)...
start "视觉检测程序-8766端口" cmd /k "cd /d %~dp0augmentwebsoket05取消红色灯珠重构01 && python main.py"
timeout /t 5 /nobreak >nul

echo [5/6] 测试WebSocket连接...
cd /d "%~dp0augmentwebsoket05取消红色灯珠重构01"
python test_websocket_v2.py client
timeout /t 3 /nobreak >nul

echo [6/7] 启动Flask Web服务器...
start "Flask Web服务器-CORS修复版" cmd /k "cd /d %~dp0 && python app.py"
timeout /t 5 /nobreak >nul

echo [7/7] 测试CORS修复效果...
python 测试CORS修复.py

echo.
echo ✓ 所有服务已重新启动并修复CORS问题！
echo.
echo 现在请：
echo 1. 清除浏览器缓存 (Ctrl+Shift+R)
echo 2. 刷新网页
echo 3. 点击"连接设备"按钮测试
echo 4. 检查是否还有CORS错误
echo.
echo 修复内容：
echo - 在Flask应用中添加了代理路由
echo - 前端改为使用相对路径请求
echo - WebSocket端口改为8766避免冲突
echo.
pause
