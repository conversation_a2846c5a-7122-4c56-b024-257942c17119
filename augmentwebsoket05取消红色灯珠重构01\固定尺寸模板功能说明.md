# 固定尺寸模板功能说明

## 功能概述

在 LED ROI 选择模式下，新增了快捷键 "A" 来启用固定尺寸模板功能。该功能允许用户快速生成指定像素大小的选择框，方便批量创建大小一致的 ROI。

## 使用方法

### 1. 进入 LED ROI 选择模式
- 启动程序后，按 'L' 进入 LED ROI 选择模式

### 2. 启用固定尺寸模板
- 在 LED ROI 选择界面，按 'A' 键启用固定尺寸模板模式
- 系统会显示提示：`进入固定尺寸模板模式，模板大小: 12x12`

### 3. 使用固定尺寸模板
- 启用后，鼠标移动时会显示黄色虚线预览框（12x12 像素）
- 点击鼠标左键在当前位置放置 ROI
- 可以连续点击多个位置，快速创建多个相同大小的 ROI

### 4. 确认和继续
- 按 'Enter' 键确认当前 ROI 并继续下一个
- 固定尺寸模板模式会保持激活，可以继续复制下一个 ROI

### 5. 退出固定尺寸模板模式
- 按 'Esc' 键退出固定尺寸模板模式
- 按 'N' 键跳过当前 ROI 时也会退出模板模式
- 按 'R' 键重置时会清空所有模板状态

## 快捷键说明

在 LED ROI 选择模式下：

| 快捷键 | 功能 |
|--------|------|
| A | 启用固定尺寸模板模式 |
| T | 启用复制模板模式（复制上一个 ROI 的大小） |
| Enter | 确认当前 ROI |
| N | 跳过当前 ROI |
| B | 回退到上一个 ROI |
| R | 重置所有 ROI |
| Esc | 退出模板模式或返回上级菜单 |

## 配置说明

### 默认模板大小
- 默认固定模板大小：12x12 像素
- 该值保存在配置文件中，程序重启后会自动加载

### 修改模板大小
目前模板大小通过配置文件修改：
1. 找到 `combined_config.json` 文件
2. 在 `led_settings` 部分找到 `fixed_template_size` 字段
3. 修改数值（例如：`"fixed_template_size": 16` 表示 16x16 像素）
4. 重启程序生效

## 视觉区别

- **固定尺寸模板预览**：黄色虚线边框
- **复制模板预览**：白色虚线边框
- **正在选择的 ROI**：蓝色实线边框

## 优势

1. **大小一致**：所有 ROI 都是相同的固定尺寸
2. **操作快速**：只需点击位置，无需拖拽
3. **精确定位**：预览框帮助精确定位
4. **批量操作**：可以连续快速创建多个 ROI
5. **配置保存**：模板大小设置会自动保存

## 注意事项

1. 固定尺寸模板模式和复制模板模式不能同时启用
2. 模板大小建议根据实际 LED 尺寸设置，通常 10-20 像素比较合适
3. 如果 LED 尺寸差异较大，建议使用传统拖拽方式或复制模板模式
4. 固定尺寸模板适合批量处理相同规格的 LED

## 实现细节

### 新增文件修改
1. `constants.py`：添加 `DEFAULT_FIXED_TEMPLATE_SIZE = 12`
2. `app_state.py`：添加固定模板相关状态变量
3. `ui_handler.py`：添加 A 键处理逻辑和模板预览
4. `config_manager.py`：添加配置保存和加载支持

### 最小修改原则
- 复用现有的模板系统架构
- 扩展而非重写现有功能
- 保持向后兼容性
