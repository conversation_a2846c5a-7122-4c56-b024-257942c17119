from flask import Flask, render_template, jsonify, request, send_from_directory, session, redirect, url_for
from flask_cors import CORS
from routes.cpu_controller import cpu_controller_bp
from routes.cpu_controllervue import cpu_controllervue_bp
from routes.io_module import io_module_bp
from routes.io_modulevue import io_modulevue_bp
from routes.coupler import coupler_bp
from routes.coupler_controllervue import coupler_controllervue_bp
from routes.board_test import board_test_bp
from routes.fault_entry import fault_entry_bp
from routes.fault_code import fault_code_bp
from routes.document_center import document_center_bp
from routes.barcode_comparison import barcode_comparison_bp
from routes.barcode_binding import barcode_binding_bp

from routes.batch_query import batch_query_bp
from routes.product_test_query import product_test_query_bp
from routes.fault_query import fault_query_bp
from routes.dashboard import dashboard_bp
from routes.product_management import product_management_bp
from routes.work_order import work_order_bp
from routes.shipment_barcode import shipment_barcode_bp
from routes.shipment_query import shipment_query_bp
from routes.quality_inspection import quality_inspection_bp
from routes.after_sales_query import after_sales_query_bp
from routes.sn_print_record_controller import sn_print_record_bp
from routes.marking_record_routes import marking_record_bp
from routes.inspection_record import inspection_record_bp
# 固件管理相关蓝图导入
from routes.firmware_all import firmware_all_bp
from routes.firmware_pending import firmware_pending_bp
from routes.firmware_usage import usage_bp
from routes.firmware_obsolete import obsolete_bp
from routes.firmware_serial_entry import firmware_serial_entry_bp
from routes.version_comparison import version_comparison_bp
from functools import wraps
import datetime
import os
import sys

# 修改 jwt 导入方式
from jwt import encode, decode

# 在文件开头添加导入
from database.db_manager import DatabaseManager
from models.user import User
from datetime import datetime, timedelta
import hashlib
import logging
from models.assembly import BoardAssembly, CompleteProduct
from database.db_manager import Base, DatabaseManager
from utils.product_mapping import ProductMapping
from config import JWT_SECRET

# 在文件开头设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'your-secret-key'  # 更改为一个安全的密钥

# 配置CORS以支持跨域访问
CORS(app, resources={
    r"/proxy/*": {
        "origins": ["http://**********:3308", "http://localhost:3308", "http://127.0.0.1:3308"],
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization"],
        "supports_credentials": True
    },
    r"/api/*": {
        "origins": ["http://**********:3308", "http://localhost:3308", "http://127.0.0.1:3308"],
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization"],
        "supports_credentials": True
    }
})

# 设置文件上传大小限制为500MB
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB

# 添加文件过大错误处理固件上传
@app.errorhandler(413)
def too_large(e):
    return jsonify({
        'success': False,
        'message': '文件过大，当前最大支持500MB，请压缩文件后重试',
        'code': 413
    }), 413

# 注册所有蓝图
app.register_blueprint(cpu_controller_bp, url_prefix='/api/cpu-controller')
app.register_blueprint(cpu_controllervue_bp, url_prefix='/api/cpu-controller-vue')
app.register_blueprint(io_module_bp, url_prefix='/api/io-module')
app.register_blueprint(io_modulevue_bp, url_prefix='/api/io-modulevue')
app.register_blueprint(coupler_bp, url_prefix='/api/coupler')
app.register_blueprint(coupler_controllervue_bp, url_prefix='/api/coupler-controller-vue')
app.register_blueprint(board_test_bp, url_prefix='/api/board-test')
app.register_blueprint(fault_entry_bp, url_prefix='/api/fault-entry')
app.register_blueprint(fault_code_bp, url_prefix='/api/fault-code')
app.register_blueprint(document_center_bp, url_prefix='/api/document-center')
app.register_blueprint(barcode_comparison_bp, url_prefix='/api/barcode-comparison')
app.register_blueprint(barcode_binding_bp, url_prefix='/api/barcode-binding')
app.register_blueprint(batch_query_bp, url_prefix='/api/batch-query')
app.register_blueprint(product_test_query_bp, url_prefix='/api/product-test-query')
app.register_blueprint(fault_query_bp, url_prefix='/api/fault')
app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
app.register_blueprint(product_management_bp)
app.register_blueprint(work_order_bp, url_prefix='/api/work-order')
app.register_blueprint(shipment_barcode_bp, url_prefix='/api/shipment-barcode')
app.register_blueprint(shipment_query_bp, url_prefix='/api/shipment-query')
app.register_blueprint(quality_inspection_bp, url_prefix='/api/quality-inspection')
app.register_blueprint(after_sales_query_bp, url_prefix='/api/after-sales-query')
app.register_blueprint(sn_print_record_bp, url_prefix='/api/sn-print-record')
app.register_blueprint(marking_record_bp)
app.register_blueprint(inspection_record_bp)

# 注册固件管理相关蓝图
app.register_blueprint(firmware_all_bp)          # 所有固件页面API，已在蓝图中定义前缀：/api/firmware/all
app.register_blueprint(firmware_pending_bp)      # 待审核页面API，前缀：/api/firmware/pending  
app.register_blueprint(usage_bp)                 # 使用记录页面API，前缀：/api/firmware/usage
app.register_blueprint(obsolete_bp)              # 作废版本页面API，前缀：/api/firmware/obsolete
app.register_blueprint(firmware_serial_entry_bp)  # 序列号录入页面API，前缀：/api/firmware/serial-entry
app.register_blueprint(version_comparison_bp)     # 版本比对页面API，前缀：/api/version-comparison

# 登录装饰器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.cookies.get('token')
        if not token:
            return redirect(url_for('login'))
        try:
            decode(token, JWT_SECRET, algorithms=["HS256"])
        except:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/login')
def login():
    return render_template('login.html')

# 修改登录接口
@app.route('/api/login', methods=['POST'])
def api_login():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        remember_me = data.get('remember_me', False)
        
        logger.debug(f"Login attempt for user: {username}")
        
        # 对密码进行SHA256加密
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        logger.debug(f"Hashed password: {hashed_password}")
        
        db = DatabaseManager()
        with db.get_session() as session:
            # 先查询用户，不带密码验证
            user = session.query(User).filter_by(username=username).first()
            
            if not user:
                logger.debug(f"User {username} not found in database")
                return jsonify({'success': False, 'message': '用户名或密码错误'}), 401
            
            logger.debug(f"Database password: {user.password}")
            logger.debug(f"Input password hash: {hashed_password}")
            
            if user.password == hashed_password:
                # 更新最后登录时间
                user.update_login_time()
                session.commit()
                
                # 设置过期时间
                expiration = datetime.utcnow() + (
                    timedelta(days=1) if remember_me 
                    else timedelta(hours=8)
                )
                
                token = encode({
                    'user_id': user.user_id,
                    'username': user.username,
                    'exp': expiration
                }, JWT_SECRET, algorithm="HS256")
                
                if isinstance(token, bytes):
                    token = token.decode('utf-8')
                    
                response = jsonify({'success': True, 'token': token})
                
                # 设置cookie
                if remember_me:
                    response.set_cookie('token', token, httponly=True, max_age=86400)
                else:
                    response.set_cookie('token', token, httponly=True)
                
                logger.debug("Login successful")
                return response
            else:
                logger.debug("Password mismatch")
                return jsonify({'success': False, 'message': '用户名或密码错误'}), 401
                
    except Exception as e:
        logger.error(f"Login error: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': '服务器错误，请联系管理员'}), 500

# 在现有路由之前添加新的路由
@app.route('/api/user/info')
@login_required
def get_user_info():
    token = request.cookies.get('token')
    try:
        user_data = decode(token, JWT_SECRET, algorithms=["HS256"])
        return jsonify({
            'success': True,
            'username': user_data['username']
        })
    except Exception as e:
        logger.error(f"Error getting user info: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取用户信息失败'
        }), 401

@app.route('/')
@login_required
def index():
    return render_template('index.html')

# 静态文件配置
app.static_folder = 'static'

# 处理static目录下的文件请求
@app.route('/static/<path:filename>')
def serve_static(filename):
    return send_from_directory('static', filename)

# 处理static/page_js_css目录下的文件请求
@app.route('/static/page_js_css/<path:filename>')
def serve_page_js_css(filename):
    return send_from_directory('static/page_js_css', filename)

# 在现有的app配置后添加
app.config['JWT_SECRET'] = JWT_SECRET

# 添加修改用户信息的路由
@app.route('/api/user/update', methods=['POST'])
@login_required
def update_user_info():
    token = request.cookies.get('token')
    try:
        user_data = decode(token, JWT_SECRET, algorithms=["HS256"])
        data = request.get_json()
        
        new_password = data.get('password')
        old_password = data.get('oldPassword')
        
        if not old_password:
            return jsonify({
                'success': False,
                'title': '错误',
                'message': '请输入原密码',
                'icon': 'error'
            }), 400
            
        # 对原密码进行加密验证
        hashed_old_password = hashlib.sha256(old_password.encode()).hexdigest()
        
        db = DatabaseManager()
        with db.get_session() as session:
            user = session.query(User).filter_by(user_id=user_data['user_id']).first()
            
            if not user or user.password != hashed_old_password:
                return jsonify({
                    'success': False,
                    'title': '错误',
                    'message': '原密码错误',
                    'icon': 'error'
                }), 401
            
            if new_password:
                user.password = hashlib.sha256(new_password.encode()).hexdigest()
            
            session.commit()
            
            # 生成新的token
            expiration = datetime.utcnow() + timedelta(hours=2)
            new_token = encode({
                'user_id': user.user_id,
                'username': user.username,
                'exp': expiration
            }, JWT_SECRET, algorithm="HS256")
            
            if isinstance(new_token, bytes):
                new_token = new_token.decode('utf-8')
            
            response = jsonify({
                'success': True,
                'title': '成功',
                'message': '密码修改成功',
                'icon': 'success'
            })
            response.set_cookie('token', new_token, httponly=True)
            
            return response
            
    except Exception as e:
        logger.error(f"Error updating password: {str(e)}")
        return jsonify({
            'success': False,
            'title': '错误',
            'message': '修改失败，请稍后重试',
            'icon': 'error'
        }), 500

# 在app初始化时创建表
db = DatabaseManager()
#Base.metadata.create_all(db.engine)

# 在现有路由之前添加新的路由
@app.route('/api/product-mapping/get-model')
def get_product_model():
    product_code = request.args.get('code', '').strip()
    if not product_code:
        return jsonify({
            'success': False,
            'message': '产品编码不能为空'
        })
    
    model = ProductMapping.get_model_by_code(product_code)
    if model:
        return jsonify({
            'success': True,
            'model': model
        })
    else:
        return jsonify({
            'success': False,
            'message': '未找到对应的产品型号'
        })

@app.route('/api/product-mapping/get-all')
def get_all_mappings():
    return jsonify({
        'success': True,
        'mappings': ProductMapping.get_all_mappings()
    })

@app.route('/api/product-mapping/get-series')
def get_product_series():
    product_code = request.args.get('code', '').strip()
    if not product_code:
        return jsonify({
            'success': False,
            'message': '产品编码不能为空'
        })
    
    series = ProductMapping.get_series_by_code(product_code)
    if series:
        return jsonify({
            'success': True,
            'series': series.value
        })
    else:
        return jsonify({
            'success': False,
            'message': '未找到对应的产品系列'
        })

# 处理 static/lib 目录下的文件请求
@app.route('/static/lib/<path:filename>')
def serve_lib(filename):
    """处理 static/lib 目录下的文件请求"""
    return send_from_directory('static/lib', filename)

# 在现有的静态文件路由后添加
@app.route('/static/lib/html2canvas.min.js')
def serve_html2canvas():
    return send_from_directory('static/lib', 'html2canvas.min.js')

if __name__ == '__main__':
    try:
        logger.info("Starting application...")
        app.run(debug=False, host='0.0.0.0', port=3308)
    except Exception as e:
        logger.error(f"Application failed to start: {str(e)}", exc_info=True)
        # 保持窗口打开
        input("Press Enter to exit...")
