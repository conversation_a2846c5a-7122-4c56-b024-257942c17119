@echo off
chcp 65001 >nul
echo ========================================
echo    WebSocket问题快速修复
echo ========================================
echo.

echo [1/3] 测试简化版WebSocket服务器...
cd /d "%~dp0augmentwebsoket05取消红色灯珠重构01"
python test_websocket_simple.py
timeout /t 3 /nobreak >nul

echo.
echo [2/3] 重新启动视觉检测程序...
taskkill /f /im python.exe 2>nul
timeout /t 2 /nobreak >nul
start "视觉检测程序-修复版" cmd /k "cd /d %~dp0augmentwebsoket05取消红色灯珠重构01 && python main.py"
timeout /t 5 /nobreak >nul

echo.
echo [3/3] 测试WebSocket连接...
python test_websocket_simple.py client

echo.
echo ✓ 修复完成！
echo.
echo 如果仍有问题，请：
echo 1. 检查端口8765是否被占用
echo 2. 重启所有Python程序
echo 3. 刷新网页
echo.
pause
