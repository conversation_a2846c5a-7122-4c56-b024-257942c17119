@echo off
chcp 65001 >nul
echo ========================================
echo    启动完整的视觉检测和代理服务系统
echo ========================================
echo.

echo [1/3] 启动本地代理服务器 (端口5000)...
start "本地代理服务器" cmd /k "cd /d %~dp0services && python user_local_proxy20250715last.py"
timeout /t 3 /nobreak >nul

echo [2/3] 启动视觉检测程序 (WebSocket端口8765)...
start "视觉检测程序" cmd /k "cd /d %~dp0websoket05取消红色灯珠重构01 && python main.py"
timeout /t 3 /nobreak >nul

echo [3/3] 启动Flask Web服务器 (端口3308)...
start "Flask Web服务器" cmd /k "cd /d %~dp0 && python app.py"
timeout /t 2 /nobreak >nul

echo.
echo ✓ 所有服务已启动完成！
echo.
echo 服务状态：
echo - 本地代理服务器: http://127.0.0.1:5000
echo - 视觉检测WebSocket: ws://127.0.0.1:8765
echo - Web界面: http://192.168.1.57:3308
echo.
echo 请等待所有服务完全启动后再使用...
echo 按任意键关闭此窗口
pause >nul
