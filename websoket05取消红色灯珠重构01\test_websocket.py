#!/usr/bin/env python3
"""
WebSocket功能测试脚本
用于测试视觉检测程序的WebSocket服务器功能
"""

import asyncio
import websockets
import json
import time

async def test_websocket_client():
    """测试WebSocket客户端连接"""
    uri = "ws://127.0.0.1:8765"
    
    try:
        print("正在连接到WebSocket服务器...")
        async with websockets.connect(uri) as websocket:
            print("✓ 连接成功!")
            
            # 发送状态查询
            query_message = {
                "type": "query_status",
                "data": {
                    "timestamp": time.strftime('%Y-%m-%d %H:%M:%S')
                }
            }
            
            print("发送状态查询...")
            await websocket.send(json.dumps(query_message))
            
            # 接收响应
            response = await websocket.recv()
            data = json.loads(response)
            print(f"收到响应: {data}")
            
            # 监听消息
            print("监听WebSocket消息...")
            try:
                while True:
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(message)
                    print(f"收到消息: {data['type']} - {data.get('data', {})}")
                    
            except asyncio.TimeoutError:
                print("5秒内未收到新消息，测试完成")
                
    except ConnectionRefusedError:
        print("✗ 连接失败: 请确保视觉检测程序已启动")
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def test_websocket_server():
    """测试WebSocket服务器启动"""
    try:
        from websocket_server import websocket_server
        print("正在启动WebSocket服务器...")
        websocket_server.start_server()
        print("✓ WebSocket服务器启动成功")
        
        # 模拟状态更新
        print("模拟倒计时状态更新...")
        for i in range(5, 0, -1):
            websocket_server.update_state(
                state='LOGGING',
                countdown=i,
                total_duration=5,
                message=f"测试倒计时: {i}秒",
                is_active=True
            )
            time.sleep(1)
        
        # 完成状态
        websocket_server.update_state(
            state='IDLE',
            countdown=0,
            total_duration=0,
            message="测试完成",
            is_active=False
        )
        
        print("✓ 状态更新测试完成")
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
    except Exception as e:
        print(f"✗ 服务器测试失败: {e}")

def check_dependencies():
    """检查依赖包是否安装"""
    try:
        import websockets
        print("✓ websockets 包已安装")
        return True
    except ImportError:
        print("✗ websockets 包未安装")
        print("请运行: pip install websockets")
        return False

if __name__ == "__main__":
    import sys

    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    if len(sys.argv) > 1 and sys.argv[1] == "client":
        # 测试客户端连接
        asyncio.run(test_websocket_client())
    else:
        # 测试服务器功能
        test_websocket_server()

        # 保持服务器运行
        try:
            print("WebSocket服务器正在运行，按Ctrl+C停止...")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n正在停止服务器...")
            from websocket_server import websocket_server
            websocket_server.stop_server()
            print("✓ 服务器已停止")
