#!/usr/bin/env python3
"""
测试CORS修复效果
"""

import requests
import time

def test_local_proxy():
    """测试本地代理服务器"""
    print("🔍 测试本地代理服务器 (端口5000)...")
    
    try:
        response = requests.get('http://127.0.0.1:5000/', timeout=5)
        if response.status_code == 200:
            print("✅ 本地代理服务器正常运行")
            return True
        else:
            print(f"⚠️ 本地代理服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 本地代理服务器连接失败: {e}")
        return False

def test_flask_proxy():
    """测试Flask应用的代理路由"""
    print("🔍 测试Flask应用代理路由 (端口3308)...")
    
    try:
        # 测试设备信息代理
        response = requests.get(
            'http://**********:3308/proxy/device-info?ip=*************',
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Flask代理路由工作正常")
                print(f"📊 设备信息: {data.get('data', {})}")
                return True
            else:
                print(f"⚠️ Flask代理返回错误: {data.get('message')}")
                return False
        else:
            print(f"❌ Flask代理响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Flask代理连接失败: {e}")
        return False

def test_direct_access():
    """测试直接访问本地代理"""
    print("🔍 测试直接访问本地代理...")
    
    try:
        response = requests.get(
            'http://127.0.0.1:5000/proxy/device-info?ip=*************',
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 直接访问本地代理成功")
                return True
            else:
                print(f"⚠️ 本地代理返回错误: {data.get('message')}")
                return False
        else:
            print(f"❌ 本地代理响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 直接访问本地代理失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("    CORS修复效果测试")
    print("=" * 50)
    print()
    
    results = []
    
    # 测试各项服务
    results.append(("本地代理服务器", test_local_proxy()))
    results.append(("直接访问本地代理", test_direct_access()))
    results.append(("Flask代理路由", test_flask_proxy()))
    
    print()
    print("=" * 50)
    print("    测试结果汇总")
    print("=" * 50)
    
    all_ok = True
    for service, status in results:
        status_text = "✅ 正常" if status else "❌ 异常"
        print(f"{service:<20} {status_text}")
        if not status:
            all_ok = False
    
    print()
    if all_ok:
        print("🎉 CORS问题已修复！")
        print("💡 现在可以正常连接设备了")
        print("📝 建议：")
        print("   1. 重启Flask Web服务器")
        print("   2. 清除浏览器缓存")
        print("   3. 刷新网页并测试连接设备")
    else:
        print("⚠️ 部分服务异常，请检查：")
        print("1. 确保本地代理服务器已启动")
        print("2. 确保Flask Web服务器已启动")
        print("3. 检查网络连接")
    
    print()
    input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试已取消")
    except Exception as e:
        print(f"\n测试过程出错: {e}")
        input("按回车键退出...")
