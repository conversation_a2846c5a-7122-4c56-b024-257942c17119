# 第一阶段精度优化 V1

> 适用版本：2025-08-xx 以后所有 **LED / 数码管检测** 模块
> 
> 目标：在尽量 **最小修改** 的前提下，提高 ROI 轻微偏移和室内光照波动场景下的检测稳定性。

---

## 1. 场景与痛点
1. 相机与工装位置固定，但工装在产线左右移动时整体亮度可变化 **±10 %–15 %**。
2. ROI 标注位置存在 1–3 px 偏差；直接对 ROI 全区域取均值时会被背景像素稀释。
3. 现行算法仅依赖 **绝对灰度 / 通道阈值**，在上述情况下偶发漏判或误判。

---

## 2. 优化项总览
| 优化项 | 作用 | 改动成本 | 是否必做 |
|--------|------|----------|---------|
| **A. 采样最亮 N % 像素** | 抵抗 ROI 偏移 & 杂光稀释 | 低 | ✅ 必做 |
| **B. 灰度阈值亮度系数 k** | 抵抗整体亮度 ±15 % 浮动 | 低 | ⚪ 可选（监测后决定） |
| **C. 模糊带颜色比值兜底** | 防止反光导致假亮 | 低-中 | ⚪ 备用 |

> 第一阶段仅实施 **A**；B 与 C 预留代码接口但默认关闭。

---

## 3. 详细设计

### 3.1 采样最亮 N % 像素（核心）
1. **新增常量**：`BRIGHT_PIXEL_PERCENT = 0.10`  <!-- 10 % → 可通过配置文件覆盖 -->
2. **LED 采样 (`led_capture_samples`)**
   - 对单个 ROI：
     ```python
     roi_flat = roi_area.reshape(-1, 3)              # (H*W, 3)
     gray_vals = cv2.cvtColor(roi_area, cv2.COLOR_BGR2GRAY).flatten()
     thresh = np.percentile(gray_vals, 100*(1-BRIGHT_PIXEL_PERCENT))
     mask = gray_vals >= thresh                      # 取最亮 N% 像素
     avg_color = roi_flat[mask].mean(axis=0)
     ```
3. **LED 实时检测 (`detect_led_status`)**
   - 同样在均值计算前加入最亮 N % 过滤；对性能影响 <1 ms/帧（分辨率 640×480, N=10 %）。
4. **数码管检测 (`digit_detector.py`)**
   - 处理逻辑与 LED 相同：在获取 ROI 平均亮度或阈值化前先做最亮 N % 采样。
5. **可配置化**
   - 若 `BRIGHT_PIXEL_PERCENT <= 0` 则退化为原“整 ROI 均值”逻辑。

### 3.2 灰度阈值亮度系数 k（可选）
1. **背景参考 ROI**：在 AppState 增加 `ambient_roi`（位于 LED 板框外黑区域）。
2. **计算方式**：每 100 帧取参考 ROI 灰度均值 `G_now`，与采样阶段均值 `G_ref` 比值 `k = G_now / G_ref`。
3. **应用**：在比较阈值时 `gray_th_eff = gray_th * k`。
4. **安全范围**：`k ∈ [0.85, 1.15]`，变化 <3 % 不生效。

### 3.3 模糊带颜色比值兜底（备用）
1. 若 `(gray_th - 5) < gray_val < (gray_th + 5)`
2. 绿色 LED: `G / (R+B+1e-6) ≥ 1.8`
3. 红色 LED: `R / (G+B+1e-6) ≥ 1.8`

> B、C 在第一阶段**不默认开启**，只提供函数与配置开关。

---

## 4. 代码修改概要
| 文件 | 变更 | 关键行 |
|------|------|-------|
| `constants.py` | 新增 `BRIGHT_PIXEL_PERCENT`、`BRIGHTNESS_K_ENABLE` 等开关 | 末尾 |
| `led_detector.py` | 修改 ROI 取均值逻辑；插入可选 k、自定义比值判断函数 | §261, 274, 279 等 |
| `digit_detector.py` | 同步最亮 N % 采样逻辑 | ROI 均值计算处 |
| `app_state.py` | 新增 `ambient_roi`, `ambient_gray_ref` 等 | 类定义 |

---

## 5. 风险控制 & 回滚
1. **单开关控制**：所有新逻辑受 `ENABLE_PRECISION_OPT_STAGE1` 统一控制，出问题可一键回退。
2. **日志追踪**：打开 DEBUG 时输出 `bright_percentile`, `k 值`, `ratio 判定` 等关键指标。
3. **性能监控**：启动后记录平均帧处理耗时，若增加 >2 ms 即触发告警。

---

## 6. 测试用例
| 类别 | 用例 | 预期 |
|------|------|------|
| ROI 偏移 | ROI 边缘裁 2 px | 误判率≤原先 20 % |
| 亮度变化 | 环灯亮度 ±15 % | 误判率无明显上升 |
| LED 全灭/全亮 | 应全部正确识别 | 100 % 准确 |
| 性能 | 30 fps 输入 | 帧率下降≤1 fps |

---

## 7. 上线步骤
1. 合并代码分支 `feature/precision-opt-v1` ➜ 预发布。  
2. 产线 A/B 测试 3 天，收集日志 → 确认 KPI。  
3. 若通过则开启 `ENABLE_PRECISION_OPT_STAGE1` 开关并部署全线。

---

## 8. 里程碑
| 日期 | 任务 |
|------|------|
| T+0 | 方案评审通过 |
| T+3 | 完成代码开发 & 单元测试 |
| T+5 | 预发布环境集成测试 |
| T+8 | 产线灰度部署 |
| T+11| 全量上线 & 结项 |

---

## 9. 附录
1. **参考文献**：
   - [OpenCV ROI brightness percentile](https://docs.opencv.org/)  
2. **缩写解释**：
   - ROI：Region of Interest  
   - SNR：Signal-to-Noise Ratio 