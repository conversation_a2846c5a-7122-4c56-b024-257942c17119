import cv2
import numpy as np
import time
import json
import os
import logging # <--- 添加导入

# --- 全局变量 ---
rois = []  # 存储 ROI 坐标 (x, y, w, h)
selecting_roi = False
ref_point = []
current_frame = None
# reference_image_off = None # 移除旧的单帧参考
# analysis_image_on = None   # 移除旧的单帧参考
max_rois = 4                # 默认最大 ROI 数量，可以动态修改
# 新增：用于存储颜色样本
off_state_samples = [] # List of lists: [roi1_samples, roi2_samples, ...] where roiN_samples is [sample1_bgr, sample2_bgr, ...]
on_state_samples = []
cap = None # 全局摄像头对象，以便在模式间传递

# --- 摄像头参数 ---
CAMERA_INDEX = 0          # 外部摄像头索引 (尝试改为 1 如果有外部摄像头)
FALLBACK_CAMERA_INDEX = 0  # 备用摄像头索引
resolution_presets = [
    (640, 480),      # VGA
    (800, 600),      # SVGA
    (1280, 720),     # HD 720p
    (1920, 1080),    # Full HD 1080p
    (2560, 1440),    # QHD/WQHD (2K) - 可能需要高性能摄像头支持
]
current_resolution_index = 3 # 默认使用 1920x1080
exposure_value = -6.0       # 初始曝光值 (根据图片)
brightness_value = 0.0      # 初始亮度值 (根据图片)
EXPOSURE_STEP = 1.0         # 曝光调整步长
BRIGHTNESS_STEP = 10.0      # 亮度调整步长

# --- 显示参数 (不再固定窗口大小) ---
# DISPLAY_WIDTH = 1024       # 显示窗口宽度 (不再强制使用)
# DISPLAY_HEIGHT = 768       # 显示窗口高度 (不再强制使用)

# --- 阈值设置 ---
GRAY_THRESHOLD = 160       # 灰度阈值（默认值）
GREEN_THRESHOLD = 180      # 绿色通道阈值（默认值）
THRESHOLD_STEP = 5         # 阈值调整步长

# --- 配置文件路径 ---
CAMERA_CONFIG_FILE = "camera_settings.json"
ROI_CONFIG_FILE = "roi_data.json"
# CONFIG_FILE = "led_config.json" # 旧文件，不再使用

# --- 窗口名称 ---
SETTINGS_WINDOW = "摄像头设置 - 按 'h' 获取帮助"
# CALIBRATION_WINDOW = "LED ROI 标定 - 按 'h' 获取帮助" # 重命名为 ROI 标注模式
ROI_ANNOTATION_WINDOW = "ROI 标注与采样 - 按 'h' 获取帮助"
DETECTION_WINDOW = "LED 检测模式 - 按 'q' 退出, 按 'c' 返回标定"

# --- 程序模式 ---
MODE_CAMERA_SETTINGS = -1 # 新增摄像头设置模式
# MODE_CALIBRATION = 0      # 标定模式 (重命名为 ROI 标注)
MODE_ROI_ANNOTATION = 0   # ROI 标注与采样模式
MODE_DETECTION = 1        # 检测模式
current_mode = MODE_CAMERA_SETTINGS # 默认启动模式

# --- 新的保存/加载函数 ---
def save_camera_settings():
    """保存摄像头设置到 camera_settings.json"""
    global current_resolution_index, exposure_value, brightness_value
    config = {
        "resolution_index": current_resolution_index,
        "exposure": exposure_value,
        "brightness": brightness_value,
        "last_update": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    try:
        with open(CAMERA_CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=4)
        print(f"摄像头设置已保存到 {CAMERA_CONFIG_FILE}")
        return True
    except Exception as e:
        print(f"保存摄像头设置失败: {e}")
        return False

def load_camera_settings():
    """从 camera_settings.json 加载摄像头设置"""
    global current_resolution_index, exposure_value, brightness_value
    # 先设置代码中的默认值
    default_res_index = 3 # 1920x1080
    default_exposure = -6.0
    default_brightness = 0.0
    current_resolution_index = default_res_index
    exposure_value = default_exposure
    brightness_value = default_brightness

    if not os.path.exists(CAMERA_CONFIG_FILE):
        print(f"摄像头配置文件 {CAMERA_CONFIG_FILE} 不存在，使用默认设置。")
        return False

    try:
        with open(CAMERA_CONFIG_FILE, 'r') as f:
            config = json.load(f)
        # 加载时使用代码中的默认值作为 fallback
        loaded_res_index = config.get("resolution_index", default_res_index)
        # 确保加载的分辨率索引在有效范围内
        if loaded_res_index >= len(resolution_presets):
            print(f"警告：配置文件中的分辨率索引 {loaded_res_index} 无效，使用默认值 {default_res_index}")
            current_resolution_index = default_res_index
        else:
            current_resolution_index = loaded_res_index
        exposure_value = config.get("exposure", default_exposure)
        brightness_value = config.get("brightness", default_brightness)
        print(f"已从 {CAMERA_CONFIG_FILE} 加载摄像头设置:")
        print(f"分辨率索引: {current_resolution_index}, 曝光: {exposure_value}, 亮度: {brightness_value}")
        return True
    except Exception as e:
        print(f"加载摄像头设置失败: {e}，使用默认设置。")
        # 确保使用默认值
        current_resolution_index = default_res_index
        exposure_value = default_exposure
        brightness_value = default_brightness
        return False

# --- 保存/加载 ROI 数据 (包括坐标、样本、阈值) ---
def save_roi_data(off_samples=None, on_samples=None):
    """保存 ROI 数据 (坐标, 样本, 阈值) 到 roi_data.json"""
    global rois, GRAY_THRESHOLD, GREEN_THRESHOLD
    # 尝试加载现有数据，以便只更新部分内容
    data = {}
    if os.path.exists(ROI_CONFIG_FILE):
        try:
            with open(ROI_CONFIG_FILE, 'r') as f:
                data = json.load(f)
        except Exception as e:
            print(f"警告：读取现有 ROI 数据文件 {ROI_CONFIG_FILE} 失败: {e}")

    # 更新数据
    data["rois"] = rois # 总是保存最新的坐标
    data["gray_threshold"] = GRAY_THRESHOLD
    data["green_threshold"] = GREEN_THRESHOLD
    if off_samples is not None:
        # 将 numpy 数组转换为列表以便 JSON 序列化
        data["off_samples"] = [[list(map(float, sample)) for sample in roi_samples] for roi_samples in off_samples]
    elif "off_samples" in data: # 如果不提供新样本，保留旧样本
        pass
    if on_samples is not None:
        data["on_samples"] = [[list(map(float, sample)) for sample in roi_samples] for roi_samples in on_samples]
    elif "on_samples" in data:
        pass
    data["last_update"] = time.strftime("%Y-%m-%d %H:%M:%S")

    try:
        with open(ROI_CONFIG_FILE, 'w') as f:
            json.dump(data, f, indent=4)
        print(f"ROI 数据已保存到 {ROI_CONFIG_FILE}")
        return True
    except Exception as e:
        print(f"保存 ROI 数据失败: {e}")
        return False

def load_roi_data():
    """从 roi_data.json 加载 ROI 数据 (坐标, 样本, 阈值)"""
    global rois, GRAY_THRESHOLD, GREEN_THRESHOLD, off_state_samples, on_state_samples
    # 定义样本变量，即使文件不存在或加载失败也有默认值
    loaded_off_samples = None
    loaded_on_samples = None
    # 设置默认阈值
    GRAY_THRESHOLD = 160
    GREEN_THRESHOLD = 180
    rois = []
    off_state_samples = [] # 初始化为空列表
    on_state_samples = []  # 初始化为空列表

    if not os.path.exists(ROI_CONFIG_FILE):
        print(f"ROI 数据文件 {ROI_CONFIG_FILE} 不存在，使用默认 ROI/阈值。")
        # 保持全局变量为默认值
        return False, None, None # 返回加载失败，无样本数据

    try:
        with open(ROI_CONFIG_FILE, 'r') as f:
            data = json.load(f)
        rois = data.get("rois", [])
        GRAY_THRESHOLD = data.get("gray_threshold", 160) # 使用默认阈值作为 fallback
        GREEN_THRESHOLD = data.get("green_threshold", 180)
        # 加载样本并转换回 numpy 数组 (如果存在)
        if "off_samples" in data and data["off_samples"]:
             # 确保内部也是列表或元组
             loaded_off_samples = [[np.array(sample) for sample in roi_samples if isinstance(sample, (list, tuple))] for roi_samples in data["off_samples"]]
             off_state_samples = loaded_off_samples # 更新全局变量
        if "on_samples" in data and data["on_samples"]:
             loaded_on_samples = [[np.array(sample) for sample in roi_samples if isinstance(sample, (list, tuple))] for roi_samples in data["on_samples"]]
             on_state_samples = loaded_on_samples # 更新全局变量

        print(f"已从 {ROI_CONFIG_FILE} 加载 ROI 数据:")
        print(f"加载了 {len(rois)} 个 ROI, 灰度阈值: {GRAY_THRESHOLD}, 绿色通道阈值: {GREEN_THRESHOLD}")
        if off_state_samples:
            print(f"加载了 {len(off_state_samples)} 个灭灯状态样本组。")
        if on_state_samples:
            print(f"加载了 {len(on_state_samples)} 个亮灯状态样本组。")
        return True, loaded_off_samples, loaded_on_samples # 返回成功及样本数据
    except Exception as e:
        print(f"加载 ROI 数据失败: {e}")
        # 加载失败时重置为默认值
        rois = []
        GRAY_THRESHOLD = 160
        GREEN_THRESHOLD = 180
        off_state_samples = []
        on_state_samples = []
        return False, None, None

# --- 应用摄像头设置 ---
# 修改：接收摄像头索引，返回新的 cap 对象
def apply_camera_settings(cam_idx):
    """创建新的摄像头对象并应用当前的分辨率、曝光和亮度设置"""
    global current_resolution_index, exposure_value, brightness_value

    width, height = resolution_presets[current_resolution_index]
    print(f"尝试创建摄像头对象并设置分辨率: {width}x{height}, 曝光: {exposure_value}, 亮度: {brightness_value}")

    # 创建新的 VideoCapture 对象
    # 注意：可以尝试指定 API Preference，例如 cv2.CAP_DSHOW 或 cv2.CAP_MSMF
    # cap = cv2.VideoCapture(cam_idx, cv2.CAP_DSHOW)
    cap = cv2.VideoCapture(cam_idx)
    if not cap.isOpened():
        print(f"错误：无法在 apply_camera_settings 中打开摄像头索引 {cam_idx}")
        return None # 返回 None 表示失败

    # 设置分辨率
    res_set_w = cap.set(cv2.CAP_PROP_FRAME_WIDTH, width)
    res_set_h = cap.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
    if not res_set_w or not res_set_h:
         print(f"警告: 可能无法完全设置分辨率 {width}x{height}。")


    # 尝试关闭自动曝光并设置手动曝光值
    # 注意：cv2.CAP_PROP_AUTO_EXPOSURE 的值可能因后端而异 (0, 1, 0.25, 0.75)
    # 0.25 通常用于 V4L2 后端关闭自动曝光
    auto_exposure_set = cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)
    if not auto_exposure_set:
        print("警告: 可能无法关闭自动曝光 (尝试值 0.25)。")
        # 尝试其他值
        auto_exposure_set = cap.set(cv2.CAP_PROP_AUTO_EXPOSURE, 1) # 尝试值 1
        if not auto_exposure_set:
             print("警告: 尝试值 1 关闭自动曝光也失败。")
        else:
             print("信息: 使用值 1 关闭自动曝光成功。")
    else:
        print("信息: 使用值 0.25 关闭自动曝光成功。")

    exposure_set = cap.set(cv2.CAP_PROP_EXPOSURE, exposure_value)
    if not exposure_set:
        print(f"警告: 可能无法设置曝光值 {exposure_value}。")

    # 设置亮度
    brightness_set = cap.set(cv2.CAP_PROP_BRIGHTNESS, brightness_value)
    if not brightness_set:
        print(f"警告: 可能无法设置亮度值 {brightness_value}。")

    # 等待设置生效
    time.sleep(1)

    # 读取并打印实际值以供调试
    actual_width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
    actual_height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
    actual_exposure = cap.get(cv2.CAP_PROP_EXPOSURE)
    actual_brightness = cap.get(cv2.CAP_PROP_BRIGHTNESS)
    print(f"实际应用的分辨率: {int(actual_width)}x{int(actual_height)}")
    print(f"实际应用的曝光值: {actual_exposure}")
    print(f"实际应用的亮度值: {actual_brightness}")
    print("-" * 20)
    return cap # 返回新创建并配置好的 cap 对象

# --- 鼠标回调函数（用于 ROI 标注模式）---
def select_roi_callback(event, x, y, flags, param):
    global ref_point, selecting_roi, rois, current_frame, max_rois

    # 不再需要缩放计算，直接使用鼠标事件坐标 (x, y)
    # h, w = current_frame.shape[:2]
    # scale_x = w / DISPLAY_WIDTH # 移除
    # scale_y = h / DISPLAY_HEIGHT # 移除

    if event == cv2.EVENT_LBUTTONDOWN:
        if len(rois) < max_rois:
            # 直接使用 x, y
            ref_point = [(x, y)]
            selecting_roi = True
        else:
            print(f"已选择 {max_rois} 个 ROI。按 'r' 重置或按 '+'/'-' 调整 ROI 数量。")

    elif event == cv2.EVENT_LBUTTONUP:
        if selecting_roi:
            # 直接使用 x, y
            ref_point.append((x, y))
            selecting_roi = False
            
            # 确保 x1 < x2, y1 < y2
            x1, y1 = ref_point[0]
            x2, y2 = ref_point[1]
            roi_x = min(x1, x2)
            roi_y = min(y1, y2)
            roi_w = abs(x1 - x2)
            roi_h = abs(y1 - y2)

            # 简单的有效性检查
            if roi_w > 5 and roi_h > 5:
                rois.append((roi_x, roi_y, roi_w, roi_h))
                print(f"ROI {len(rois)} 添加: x={roi_x}, y={roi_y}, w={roi_w}, h={roi_h}")
            else:
                print("选择的 ROI 太小，请重新选择。")

    elif event == cv2.EVENT_MOUSEMOVE:
        if selecting_roi:
            # 在当前帧副本上绘制临时矩形
            temp_frame = current_frame.copy()
            # 直接使用 ref_point[0] 和 (x, y)
            cv2.rectangle(temp_frame, ref_point[0], (x, y), (0, 255, 0), 1)
            cv2.imshow(ROI_ANNOTATION_WINDOW, temp_frame) # 更新窗口名称

# --- 摄像头设置模式函数 --- (接收 cap 和 cam_idx)
def camera_settings_mode(initial_cap, cam_idx):
    """处理摄像头分辨率、曝光、亮度的设置"""
    global current_mode, current_resolution_index, exposure_value, brightness_value, cap # 声明 cap 为全局变量
    cap = initial_cap # 使用传入的 cap 对象开始

    cv2.namedWindow(SETTINGS_WINDOW, cv2.WINDOW_NORMAL)
    # cv2.resizeWindow(SETTINGS_WINDOW, DISPLAY_WIDTH, DISPLAY_HEIGHT) # 移除固定窗口大小

    while current_mode == MODE_CAMERA_SETTINGS:
        ret, frame = cap.read()
        if not ret:
            print("错误：无法读取摄像头帧 (设置模式)。")
            time.sleep(1)
            continue

        # 不再强制缩放显示帧
        display_frame = frame.copy() 

        # 显示当前设置信息
        res_w, res_h = resolution_presets[current_resolution_index]
        settings_text = [
            f"分辨率: {res_w}x{res_h} ({current_resolution_index+1}/{len(resolution_presets)})",
            f"曝光: {exposure_value:.1f}",
            f"亮度: {brightness_value:.1f}"
        ]
        cv2.putText(display_frame, " | ".join(settings_text), (10, 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 1)

        # 添加按键提示
        cv2.putText(display_frame, "按 't'/'T' 切换分辨率", (10, 50), # 更新提示
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        cv2.putText(display_frame, "按 'e'/'E' 调整曝光", (10, 70),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        cv2.putText(display_frame, "按 'b'/'B' 调整亮度", (10, 90), # 修正缩进
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        cv2.putText(display_frame, "按 'd' 或 'Enter' 确认设置并进入 ROI 标注", (10, 110), # 更新提示
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        cv2.putText(display_frame, "按 'h' 获取帮助 | 按 'q' 退出", (10, 130),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)


        cv2.imshow(SETTINGS_WINDOW, display_frame)
        key = cv2.waitKey(1) & 0xFF

        # --- 按键处理 ---
        if key == ord('q'):
            print("退出程序...")
            return False, cap # 结束主循环，返回当前 cap
        elif key == ord('h'):
            print("\n--- 摄像头设置模式操作指南 ---")
            # 修正这里的缩进
            print("  - 't'/'T': 减少/增加分辨率档位")
            print("  - 'e'/'E': 减少/增加曝光值")
            print("  - 'b'/'B': 减少/增加亮度值")
            print("  - 'd' 或 'Enter': 确认当前设置，进入 ROI 标注模式")
            print("  - 's': 保存当前摄像头设置") # 只保存摄像头设置
            print("  - 'h': 显示此帮助信息")
            print("  - 'q': 退出程序")

        # 分辨率切换 (使用 t/T) - 释放并重新创建 cap
        elif key == ord('t'): # 降低分辨率
            current_resolution_index = (current_resolution_index - 1 + len(resolution_presets)) % len(resolution_presets)
            if cap:
                print("释放旧摄像头对象...")
                cap.release()
            print("尝试应用新设置并获取新摄像头对象...")
            cap = apply_camera_settings(cam_idx) # 获取新的 cap 对象
            if cap is None: # 如果创建失败
                print("错误：无法重新初始化摄像头，退出设置模式。")
                return False, None # 返回失败状态和 None cap
            time.sleep(0.2) # 在读取下一帧前增加短暂延时
        elif key == ord('T'): # 增加分辨率
            current_resolution_index = (current_resolution_index + 1) % len(resolution_presets)
            if cap:
                print("释放旧摄像头对象...")
                cap.release()
            print("尝试应用新设置并获取新摄像头对象...")
            cap = apply_camera_settings(cam_idx) # 获取新的 cap 对象
            if cap is None: # 如果创建失败
                print("错误：无法重新初始化摄像头，退出设置模式。")
                return False, None # 返回失败状态和 None cap
            time.sleep(0.2) # 在读取下一帧前增加短暂延时

        # 曝光调整 (直接在当前 cap 上设置)
        elif key == ord('e'): # 减少曝光
            exposure_value -= EXPOSURE_STEP
            if cap:
                print(f"尝试设置曝光: {exposure_value}")
                cap.set(cv2.CAP_PROP_EXPOSURE, exposure_value)
            time.sleep(0.1) # 曝光/亮度调整后也加一点延时
        elif key == ord('E'): # 增加曝光
            exposure_value += EXPOSURE_STEP
            if cap:
                print(f"尝试设置曝光: {exposure_value}")
                cap.set(cv2.CAP_PROP_EXPOSURE, exposure_value)
            time.sleep(0.1)

        # 亮度调整 (直接在当前 cap 上设置)
        elif key == ord('b'): # 减少亮度
            brightness_value -= BRIGHTNESS_STEP
            if cap:
                print(f"尝试设置亮度: {brightness_value}")
                cap.set(cv2.CAP_PROP_BRIGHTNESS, brightness_value)
            time.sleep(0.1)
        elif key == ord('B'): # 增加亮度
            brightness_value += BRIGHTNESS_STEP
            if cap:
                print(f"尝试设置亮度: {brightness_value}")
                cap.set(cv2.CAP_PROP_BRIGHTNESS, brightness_value)
            time.sleep(0.1)

        # 保存配置
        elif key == ord('s'):
            save_camera_settings() # 只保存摄像头设置

        # 确认设置，进入 ROI 标注模式
        elif key == ord('d') or key == 13: # 'd' or Enter key
            print("摄像头设置确认，进入 ROI 标注模式...")
            # 在切换前最后保存一次设置
            save_camera_settings()
            cv2.destroyWindow(SETTINGS_WINDOW)
            current_mode = MODE_ROI_ANNOTATION # 切换到 ROI 标注模式
            return True, cap # 返回成功状态和当前的 cap 对象

    return True, cap # 默认继续，返回当前的 cap 对象

# --- 颜色采样辅助函数 ---
def capture_samples(cap, num_frames=10):
    """捕捉指定帧数并计算每个 ROI 的平均 BGR 值"""
    global rois
    if not rois:
        print("错误：无法采样，请先定义 ROI。")
        return None

    samples_per_roi = [[] for _ in rois] # 每个 ROI 一个列表来存储样本
    frames_captured = 0
    print(f"开始采集 {num_frames} 帧样本...")

    while frames_captured < num_frames:
        ret, frame = cap.read()
        if not ret:
            print(f"警告：采集样本时无法读取帧 (第 {frames_captured+1} 帧)")
            time.sleep(0.1) # 稍等一下再试
            continue

        for i, (x, y, w, h) in enumerate(rois):
            roi_area = frame[y:y+h, x:x+w]
            if roi_area.size > 0:
                avg_color = np.mean(roi_area, axis=(0, 1)) # 计算 BGR 平均值
                samples_per_roi[i].append(avg_color)
            else:
                 print(f"警告: ROI {i+1} 在采样时无效 (帧 {frames_captured+1})")

        frames_captured += 1
        # 短暂延时避免 CPU 过载，并给摄像头时间
        time.sleep(0.05) 
        # 可以在这里加一个进度提示，但可能会刷屏
        # print(f"已采集 {frames_captured}/{num_frames} 帧")

    print("样本采集完成。")
    # 检查是否所有 ROI 都采集到了样本
    for i, samples in enumerate(samples_per_roi):
        if not samples:
            print(f"警告：ROI {i+1} 未能采集到任何有效样本。")
            # 可以选择填充默认值或引发错误
            # samples_per_roi[i] = [np.array([0,0,0])] # 填充黑色样本

    return samples_per_roi

# --- 阈值计算辅助函数 ---
def calculate_thresholds(off_samples, on_samples):
    """根据亮灭样本计算建议的灰度阈值和绿色阈值"""
    if not off_samples or not on_samples or len(off_samples) != len(on_samples):
        print("错误：亮灯或灭灯样本数据不完整或不匹配，无法计算阈值。")
        return 160, 180 # 返回默认值

    avg_gray_off_all = []
    avg_green_off_all = []
    avg_gray_on_all = []
    avg_green_on_all = []

    num_rois = len(off_samples)
    for i in range(num_rois):
        if not off_samples[i] or not on_samples[i]:
            print(f"警告：ROI {i+1} 的亮灯或灭灯样本为空，跳过此 ROI 的阈值计算。")
            continue

        # 计算每个 ROI 的平均灭灯颜色
        avg_off_bgr = np.mean(np.array(off_samples[i]), axis=0)
        avg_off_gray = cv2.cvtColor(np.uint8([[avg_off_bgr]]), cv2.COLOR_BGR2GRAY)[0][0]
        avg_off_green = avg_off_bgr[1] # BGR 中的 G
        avg_gray_off_all.append(avg_off_gray)
        avg_green_off_all.append(avg_off_green)

        # 计算每个 ROI 的平均亮灯颜色
        avg_on_bgr = np.mean(np.array(on_samples[i]), axis=0)
        avg_on_gray = cv2.cvtColor(np.uint8([[avg_on_bgr]]), cv2.COLOR_BGR2GRAY)[0][0]
        avg_on_green = avg_on_bgr[1] # BGR 中的 G
        avg_gray_on_all.append(avg_on_gray)
        avg_green_on_all.append(avg_on_green)

    if not avg_gray_off_all or not avg_gray_on_all:
        print("错误：没有足够的有效样本来计算阈值。")
        return 160, 180 # 返回默认值

    # 计算全局平均值
    mean_gray_off = np.mean(avg_gray_off_all)
    mean_green_off = np.mean(avg_green_off_all)
    mean_gray_on = np.mean(avg_gray_on_all)
    mean_green_on = np.mean(avg_green_on_all)

    # 计算建议阈值（亮灭状态的中间值）
    suggested_gray_threshold = (mean_gray_off + mean_gray_on) / 2
    suggested_green_threshold = (mean_green_off + mean_green_on) / 2

    print(f"根据样本计算出的建议阈值：灰度={suggested_gray_threshold:.2f}, 绿色={suggested_green_threshold:.2f}")
    return suggested_gray_threshold, suggested_green_threshold


# --- ROI 标注与采样模式函数 ---
def roi_annotation_mode(cap):
    """处理 ROI 标注、颜色采样和阈值计算"""
    global current_mode, rois, max_rois, current_frame, off_state_samples, on_state_samples, GRAY_THRESHOLD, GREEN_THRESHOLD
    
    # 加载现有 ROI 数据（如果存在），但不加载样本，因为我们要重新采样
    roi_loaded, loaded_off_samples, loaded_on_samples = load_roi_data() # 修改：接收加载的样本
    if roi_loaded:
        print(f"已加载 {len(rois)} 个 ROI 坐标。您可以按 'r' 重置或继续添加/修改。")
        # 如果加载了样本，也更新全局变量 (虽然下面会重置，但保持一致性)
        if loaded_off_samples: off_state_samples = loaded_off_samples
        if loaded_on_samples: on_state_samples = loaded_on_samples
    else:
        rois = [] # 确保从空列表开始

    # 重置样本列表和状态
    off_state_samples = []
    on_state_samples = []
    samples_collected_off = False
    samples_collected_on = False
    thresholds_calculated = False

    cv2.namedWindow(ROI_ANNOTATION_WINDOW, cv2.WINDOW_NORMAL)
    cv2.setMouseCallback(ROI_ANNOTATION_WINDOW, select_roi_callback)

    while current_mode == MODE_ROI_ANNOTATION:
        ret, frame = cap.read()
        if not ret:
            print("错误：无法读取摄像头帧 (ROI 标注模式)。")
            time.sleep(1)
            continue

        current_frame = frame.copy()
        display_frame = current_frame.copy()

        # 绘制已定义的 ROI
        for i, (x, y, w, h) in enumerate(rois):
            cv2.rectangle(display_frame, (x, y), (x+w, y+h), (0, 0, 255), 2)
            cv2.putText(display_frame, f"{i+1}", (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

        # 显示状态和提示信息
        status_texts = [f"ROIs: {len(rois)}/{max_rois}"]
        if len(rois) < max_rois:
             status_texts.append("请用鼠标绘制 ROI")
        else:
             status_texts.append("ROI 数量已满")

        if len(rois) == max_rois:
            if not samples_collected_off:
                status_texts.append("按 'o' 采集灭灯样本")
            else:
                status_texts.append("灭灯样本: Y")
                if not samples_collected_on:
                    status_texts.append("按 'l' 采集亮灯样本")
                else:
                    status_texts.append("亮灯样本: Y")
                    if not thresholds_calculated:
                         status_texts.append("按 'a' 分析并计算阈值")
                    else:
                         status_texts.append("阈值已计算")
                         status_texts.append("按 'd' 进入检测模式")

        # 将状态文本绘制在图像底部
        y_offset = display_frame.shape[0] - 20
        for i, text in enumerate(reversed(status_texts)):
             cv2.putText(display_frame, text, (10, y_offset - i*20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
        cv2.putText(display_frame, "按 'h' 帮助 | 'r' 重置ROI | 'q' 退出", (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)


        cv2.imshow(ROI_ANNOTATION_WINDOW, display_frame)
        key = cv2.waitKey(1) & 0xFF

        if key == ord('q'):
            print("退出程序...")
            return False # 结束主循环
        elif key == ord('h'):
             print("\n--- ROI 标注与采样模式操作指南 ---")
             print("  - 鼠标左键拖拽: 选择 ROI 区域")
             print(f"  - 需要选择 {max_rois} 个 ROI")
             print("  - 'r': 重置所有已选择的 ROI")
             print("  - '+'/'-': 增加/减少最大 ROI 数量")
             print("  --- ROI 选满后 ---")
             print("  - 'o': (Off) 采集灭灯状态的颜色样本 (约 10 帧)")
             print("  - 'l': (Light) 采集亮灯状态的颜色样本 (约 10 帧)")
             print("  - 'a': (Analyze) 分析样本，计算并保存阈值到 roi_data.json")
             print("  - 'd': (Detect) 进入检测模式 (需先完成分析)")
             print("  - 'h': 显示此帮助信息")
             print("  - 'q': 退出程序")
        elif key == ord('r'):
            rois = []
            selecting_roi = False
            ref_point = []
            off_state_samples = []
            on_state_samples = []
            samples_collected_off = False
            samples_collected_on = False
            thresholds_calculated = False
            print("ROI 和样本已重置。")
        elif key == ord('+') or key == ord('='):
            max_rois += 1
            print(f"最大 ROI 数量增加到: {max_rois}")
        elif key == ord('-') or key == ord('_'):
            if max_rois > 1:
                max_rois -= 1
                if len(rois) > max_rois: # 如果当前 ROI 数量超过新的最大值，则截断
                    rois = rois[:max_rois]
                    # 如果截断了 ROI，需要重置样本和分析状态
                    off_state_samples = []
                    on_state_samples = []
                    samples_collected_off = False
                    samples_collected_on = False
                    thresholds_calculated = False
                    print("ROI 列表已根据新的最大值截断，请重新采样和分析。")
                print(f"最大 ROI 数量减少到: {max_rois}")
            else:
                print("最小 ROI 数量为 1")
        
        # --- 采样和分析逻辑 ---
        elif len(rois) == max_rois: # 只有 ROI 选满后才能采样
            if key == ord('o') and not samples_collected_off:
                print("准备采集灭灯样本，请确保所有 LED 处于灭灯状态...")
                # 可以在采集前给用户几秒准备时间或再次按键确认
                time.sleep(1)
                off_state_samples = capture_samples(cap)
                if off_state_samples:
                    samples_collected_off = True
                    thresholds_calculated = False # 需要重新计算阈值
                else:
                    print("灭灯样本采集失败。")
            elif key == ord('l') and samples_collected_off and not samples_collected_on:
                print("准备采集亮灯样本，请确保至少一个 LED 处于亮灯状态...")
                time.sleep(1)
                on_state_samples = capture_samples(cap)
                if on_state_samples:
                    samples_collected_on = True
                    thresholds_calculated = False # 需要重新计算阈值
                else:
                    print("亮灯样本采集失败。")
            elif key == ord('a') and samples_collected_off and samples_collected_on:
                print("开始分析样本并计算阈值...")
                suggested_gray, suggested_green = calculate_thresholds(off_state_samples, on_state_samples)
                GRAY_THRESHOLD = suggested_gray
                GREEN_THRESHOLD = suggested_green
                print("尝试保存 ROI 数据（包括坐标、样本和计算出的阈值）...")
                if save_roi_data(off_state_samples, on_state_samples):
                     thresholds_calculated = True
                     print("分析完成，阈值已计算并保存。按 'd' 进入检测模式。")
                else:
                     print("保存 ROI 数据失败，请重试分析。")
            elif key == ord('d') and thresholds_calculated:
                 print("进入检测模式...")
                 cv2.destroyWindow(ROI_ANNOTATION_WINDOW)
                 current_mode = MODE_DETECTION
                 return True # 继续主循环
            elif key in [ord('o'), ord('l'), ord('a'), ord('d')]:
                 # 提示用户按顺序操作
                 if len(rois) < max_rois:
                      print(f"请先完成所有 {max_rois} 个 ROI 的标注。")
                 elif not samples_collected_off:
                      print("请先按 'o' 采集灭灯样本。")
                 elif not samples_collected_on:
                      print("请先按 'l' 采集亮灯样本。")
                 elif not thresholds_calculated:
                      print("请先按 'a' 分析样本并计算阈值。")
                 else: # 应该不会到这里，因为 'd' 的条件已经处理了
                      pass

    return True # 默认继续

# --- LED 状态检测核心函数 ---
def detect_led_status(frame, rois):
    """
    分析每个 ROI 的状态 (ON/OFF) 并返回状态列表和对应的灰度/绿色值。

    Args:
        frame: 当前摄像头帧 (numpy array)。
        rois: ROI 列表 [(x, y, w, h), ...]。

    Returns:
        tuple: (led_status, led_values)
            led_status (list): 每个 ROI 的状态列表 [True (ON), False (OFF), ...]。
            led_values (list): 每个 ROI 的 (灰度值, 绿色值) 列表 [(gray, green), ...]。
    """
    global GRAY_THRESHOLD, GREEN_THRESHOLD # 引用全局阈值
    led_status = []
    led_values = []

    if frame is None or not rois:
        return [], [] # 如果没有帧或 ROI，返回空列表

    # 先收集所有ROI的亮度值，用于后续分析
    all_gray_values = []
    all_green_values = []
    roi_areas = []

    # 第一遍循环：收集所有ROI的值
    for i, (x, y, w, h) in enumerate(rois):
        # 确保 ROI 坐标在帧内
        y_end = min(y + h, frame.shape[0])
        x_end = min(x + w, frame.shape[1])
        roi_y = max(0, y)
        roi_x = max(0, x)

        if roi_y >= y_end or roi_x >= x_end:
             print(f"警告: ROI {i+1} 坐标无效或完全在帧外。")
             all_gray_values.append(0.0)
             all_green_values.append(0.0)
             roi_areas.append(None)
             continue

        roi_area = frame[roi_y:y_end, roi_x:x_end]

        if roi_area.size == 0:
            print(f"警告: ROI {i+1} 区域为空 (可能由于坐标问题)。")
            all_gray_values.append(0.0)
            all_green_values.append(0.0)
            roi_areas.append(None)
            continue

        # 计算 ROI 内的平均 BGR 颜色
        avg_color_bgr = np.mean(roi_area, axis=(0, 1))

        # 计算灰度值
        avg_color_bgr_uint8 = np.uint8([[avg_color_bgr]])
        gray_value = cv2.cvtColor(avg_color_bgr_uint8, cv2.COLOR_BGR2GRAY)[0][0]

        # 获取绿色通道值 (BGR 顺序中 G 是索引 1)
        green_value = avg_color_bgr[1]

        all_gray_values.append(float(gray_value))
        all_green_values.append(float(green_value))
        roi_areas.append(roi_area)

    # 找出最亮的LED（假设只有一个LED真正点亮）
    max_gray_index = -1
    max_gray_value = 0
    
    # 检查最大亮度与平均亮度的差异
    valid_gray_values = [v for v in all_gray_values if v > 0]
    if valid_gray_values:
        avg_gray = sum(valid_gray_values) / len(valid_gray_values)
        # 找出最亮的LED
        for i, gray_value in enumerate(all_gray_values):
            if gray_value > max_gray_value:
                max_gray_value = gray_value
                max_gray_index = i

    # 第二遍循环：基于收集的数据，更智能地判断每个LED的状态
    for i, (gray_value, green_value) in enumerate(zip(all_gray_values, all_green_values)):
        if roi_areas[i] is None:
            led_status.append(False)
            led_values.append((0.0, 0.0))
            continue
            
        # 判断状态逻辑改进：
        # 1. 使用标准阈值判断
        standard_judgment = (gray_value >= GRAY_THRESHOLD) or (green_value >= GREEN_THRESHOLD)
        
        # 2. 增加亮度差异比较 - 如果与最亮的LED差距很大，可能是切换过程中的"余辉"
        brightness_ratio = 1.0
        if i != max_gray_index and max_gray_value > 0:
            brightness_ratio = gray_value / max_gray_value
        
        # 3. 如果亮度远低于最亮的LED（小于70%），认为它可能是切换过程中的"余辉"
        is_much_dimmer = brightness_ratio < 0.7
        
        # 最终判断：标准判断为ON，且不是明显的"余辉"
        is_on = standard_judgment and (i == max_gray_index or not is_much_dimmer)
        
        led_status.append(is_on)
        led_values.append((float(gray_value), float(green_value)))

    return led_status, led_values

# --- 检测模式函数 ---
def detection_mode(cap):
    global current_mode, GRAY_THRESHOLD, GREEN_THRESHOLD
    
    # --- 侧边栏和表格常量 ---
    SIDEBAR_WIDTH = 400  # 增加侧边栏宽度以容纳更多信息
    TABLE_START_X = 0 # 会在循环内设置为 frame_w
    TABLE_HEADER_Y = 30
    ROW_HEIGHT = 25
    COL_WIDTHS = [50, 70, 80, 80, 100] # LED#, Status, Gray, Green, Brightness%
    SIDEBAR_BG_COLOR = (40, 40, 40) # 深灰色
    HEADER_COLOR = (220, 220, 220) # 浅灰色
    TEXT_COLOR = (255, 255, 255) # 白色
    ON_COLOR = (0, 255, 0)       # 绿色
    OFF_COLOR = (0, 0, 255)      # 红色
    LINE_COLOR = (100, 100, 100) # 分隔线颜色

    # 检查是否有ROI数据 (现在从 roi_data.json 加载)
    roi_loaded, _, _ = load_roi_data() # 加载 ROI 和阈值
    if not roi_loaded or not rois:
        print("错误: 没有可用的 ROI 数据。请先进行标注和采样。")
        current_mode = MODE_ROI_ANNOTATION # 返回标注模式
        return True # 让主循环处理模式切换
        
    # 创建检测窗口
    cv2.namedWindow(DETECTION_WINDOW, cv2.WINDOW_NORMAL)  # 使用WINDOW_NORMAL允许调整窗口大小
    
    print("\n=== LED状态检测器已启动 ===")
    print("使用 ROI 坐标:")
    for i, roi in enumerate(rois):
        print(f"  ROI {i+1}: x={roi[0]}, y={roi[1]}, w={roi[2]}, h={roi[3]}")
    print(f"灰度阈值: {GRAY_THRESHOLD:.2f}, 绿色通道阈值: {GREEN_THRESHOLD:.2f}")
    print("按 'c' 返回 ROI 标注模式, 按 's' 保存当前阈值, 按 'q' 退出程序\n") # 更新提示
    
    # --- 时间控制变量 ---
    last_capture_time = time.time()
    capture_interval = 0.1  # 100 毫秒采集间隔
    # --- 时间控制变量结束 ---

    # 添加历史记录
    led_history = [[] for _ in range(len(rois))]  # 每个LED的历史记录
    HISTORY_SIZE = 5  # 保留最近5次采样

    # 增加状态变化时间记录
    last_state = [False] * len(rois)  # 初始状态全部为OFF
    last_change_time = [0.0] * len(rois)  # 上次状态变化的时间戳

    def update_led_status(led_status, led_values):
        current_timestamp = time.time()
        
        # 找出最亮的LED（可能是刚刚点亮的）
        max_gray = 0
        max_gray_index = -1
        for i, (_, values) in enumerate(zip(led_status, led_values)):
            gray_val = values[0]
            if gray_val > max_gray:
                max_gray = gray_val
                max_gray_index = i
        
        for i, (status, values) in enumerate(zip(led_status, led_values)):
            led_history[i].append((status, values))
            if len(led_history[i]) > HISTORY_SIZE:
                led_history[i].pop(0)
            
            # 使用滑动窗口判断最终状态
            if len(led_history[i]) == HISTORY_SIZE:
                # 计算灰度值的趋势
                gray_values = [val[0] for _, val in led_history[i]]
                gray_trend = gray_values[-1] - gray_values[0]  # 正值表示亮度上升，负值表示亮度下降
                
                # 如果最近5次采样中有4次以上是ON，则认为是ON（提高判断标准）
                on_count = sum(1 for s, _ in led_history[i] if s)
                
                # 更严格的判断：要求更多的ON记录
                new_status = on_count >= 4
                
                # 互斥逻辑：如果检测到最亮的LED是其他LED，且当前LED明显不太亮，则认为它是OFF
                brightness_ratio = 1.0
                if i != max_gray_index and max_gray > 0:
                    brightness_ratio = values[0] / max_gray
                    # 如果亮度低于最亮LED的60%，强制设为OFF
                    if brightness_ratio < 0.6 and max_gray > GRAY_THRESHOLD*1.2:
                        new_status = False
                
                # 检测状态变化并记录时间差
                if new_status != last_state[i]:
                    if last_change_time[i] > 0:  # 不是第一次变化
                        time_diff = current_timestamp - last_change_time[i]
                        brightness_info = f", brightness: {brightness_ratio*100:.1f}% of max" if i != max_gray_index else ", (brightest LED)"
                        if new_status:  # OFF->ON
                            logging.info(f"ROI {i+1} state OFF->ON, time since last change: {time_diff*1000:.2f}ms{brightness_info}")
                        else:  # ON->OFF
                            logging.info(f"ROI {i+1} state ON->OFF, time since last change: {time_diff*1000:.2f}ms{brightness_info}")
                    
                    last_change_time[i] = current_timestamp
                    last_state[i] = new_status
                
                led_status[i] = new_status

    # 主循环
    while current_mode == MODE_DETECTION:
        current_time = time.time()
        elapsed_time = current_time - last_capture_time

        # --- 时间控制：检查是否达到采集间隔 ---
        if elapsed_time >= capture_interval:
            last_capture_time = current_time # 更新上次采集时间

            ret, frame = cap.read()
            if not ret:
                print("无法读取摄像头帧，尝试重新连接...")
                time.sleep(1)
                continue
            
            # --- 开始帧处理和检测逻辑 ---
            # 复制帧用于绘制
            original_frame = frame.copy()
            frame_h, frame_w = original_frame.shape[:2]
            TABLE_START_X = frame_w # 更新表格起始 X 坐标

            # 创建包含侧边栏的新画布
            combined_frame_w = frame_w + SIDEBAR_WIDTH
            combined_frame = np.zeros((frame_h, combined_frame_w, 3), dtype=np.uint8)

            # 将原始帧绘制到左侧
            combined_frame[0:frame_h, 0:frame_w] = original_frame

            # 绘制侧边栏背景
            cv2.rectangle(combined_frame, (TABLE_START_X, 0), (combined_frame_w, frame_h), SIDEBAR_BG_COLOR, -1)

            # 检测每个LED的状态（使用原始尺寸的帧）
            led_status, led_values = detect_led_status(original_frame, rois)
            
            # 更新LED状态（使用滑动窗口）
            update_led_status(led_status, led_values)

            # --- 日志记录 ---
            # 以高精度时间戳记录每个LED的状态和值
            # 找出最亮的LED作为参考
            max_gray = max(v[0] for v in led_values) if led_values else 0
            
            for i, (is_on, (gray_val, green_val)) in enumerate(zip(led_status, led_values)):
                status_text = "ON" if is_on else "OFF"
                brightness_ratio = (gray_val / max_gray * 100) if max_gray > 0 else 0
                log_message = f"ROI {i+1}: Status={status_text}, Gray={gray_val:.1f}, Green={green_val:.1f}, Brightness={brightness_ratio:.1f}%"
                logging.info(log_message)
            # --- 日志记录结束 ---

            # --- 在原始帧区域绘制 ROI 和状态 ---
            for i, ((x, y, w, h), is_on, (gray_val, green_val)) in enumerate(zip(rois, led_status, led_values)):
                color = ON_COLOR if is_on else OFF_COLOR
                status_text = "ON" if is_on else "OFF"
                
                # 在 combined_frame 的左侧区域绘制
                cv2.rectangle(combined_frame, (x, y), (x+w, y+h), color, 2)
                cv2.putText(combined_frame, f"{i+1}:{status_text}", (x, y-5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

            # --- 在侧边栏绘制表格 ---
            # 绘制表头
            current_x = TABLE_START_X + 5
            headers = ["LED#", "Status", "Gray", "Green", "Brightness%"]
            for idx, header in enumerate(headers):
                cv2.putText(combined_frame, header, (current_x, TABLE_HEADER_Y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, HEADER_COLOR, 1)
                current_x += COL_WIDTHS[idx]
            # 绘制表头下的分隔线
            cv2.line(combined_frame, (TABLE_START_X, TABLE_HEADER_Y + 8), (combined_frame_w, TABLE_HEADER_Y + 8), LINE_COLOR, 1)

            # 获取最大灰度值用于计算亮度百分比
            max_gray = max(v[0] for v in led_values) if led_values else 1.0

            # 绘制每一行数据
            for i, (is_on, (gray_val, green_val)) in enumerate(zip(led_status, led_values)):
                current_x = TABLE_START_X + 5
                row_y_base = TABLE_HEADER_Y + 8 + (i + 1) * ROW_HEIGHT

                # LED 编号
                cv2.putText(combined_frame, str(i+1), (current_x, row_y_base), cv2.FONT_HERSHEY_SIMPLEX, 0.5, TEXT_COLOR, 1)
                current_x += COL_WIDTHS[0]

                # Status
                status_text = "ON" if is_on else "OFF"
                status_color = ON_COLOR if is_on else OFF_COLOR
                cv2.putText(combined_frame, status_text, (current_x, row_y_base), cv2.FONT_HERSHEY_SIMPLEX, 0.5, status_color, 1)
                current_x += COL_WIDTHS[1]

                # Gray Value
                cv2.putText(combined_frame, f"{gray_val:.1f}", (current_x, row_y_base), cv2.FONT_HERSHEY_SIMPLEX, 0.5, TEXT_COLOR, 1)
                current_x += COL_WIDTHS[2]

                # Green Value
                cv2.putText(combined_frame, f"{green_val:.1f}", (current_x, row_y_base), cv2.FONT_HERSHEY_SIMPLEX, 0.5, TEXT_COLOR, 1)
                current_x += COL_WIDTHS[3]
                
                # Brightness% (相对于最亮的LED)
                brightness_percent = (gray_val / max_gray * 100) if max_gray > 0 else 0
                brightness_color = TEXT_COLOR
                if brightness_percent > 90:  # 最亮的LED
                    brightness_color = ON_COLOR
                elif brightness_percent < 60:  # 很暗的LED
                    brightness_color = OFF_COLOR
                
                cv2.putText(combined_frame, f"{brightness_percent:.1f}%", (current_x, row_y_base), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, brightness_color, 1)
                
                # 绘制行分隔线
                cv2.line(combined_frame, (TABLE_START_X, row_y_base + 8), (combined_frame_w, row_y_base + 8), LINE_COLOR, 1)

            # --- 在原始帧区域绘制其他信息 ---
            # 显示当前阈值
            cv2.putText(combined_frame, f"Gray Threshold: {GRAY_THRESHOLD:.1f}", 
                       (10, frame_h - 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(combined_frame, f"Green Threshold: {GREEN_THRESHOLD:.1f}", 
                       (10, frame_h - 40), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(combined_frame, "Press 'g/G'/'v/V' to adjust thresholds", 
                       (10, frame_h - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(combined_frame, "Press 'c' to calibrate, 'q' to quit", 
                       (frame_w - 250, frame_h - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 显示帧 (现在是包含侧边栏的 combined_frame)
            cv2.imshow(DETECTION_WINDOW, combined_frame)
            # --- 结束帧处理和检测逻辑 ---

        # 按键检测 (始终执行以确保响应)
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            print("退出程序...")
            return False
        elif key == ord('c'):
            # 返回 ROI 标注模式
            print("返回 ROI 标注模式...")
            cv2.destroyWindow(DETECTION_WINDOW)
            current_mode = MODE_ROI_ANNOTATION # 切换到 ROI 标注模式
            return True # 让主循环处理模式切换
        # 调整灰度阈值
        elif key == ord('g'):
            GRAY_THRESHOLD = max(0, GRAY_THRESHOLD - THRESHOLD_STEP)
            print(f"灰度阈值减小到: {GRAY_THRESHOLD:.1f}")
        elif key == ord('G'):
            GRAY_THRESHOLD += THRESHOLD_STEP
            print(f"灰度阈值增加到: {GRAY_THRESHOLD:.1f}")
        # 调整绿色通道阈值 (使用 v/V)
        elif key == ord('v'):
            GREEN_THRESHOLD = max(0, GREEN_THRESHOLD - THRESHOLD_STEP)
            print(f"绿色通道阈值减小到: {GREEN_THRESHOLD:.1f}")
        elif key == ord('V'):
            GREEN_THRESHOLD += THRESHOLD_STEP
            print(f"绿色通道阈值增加到: {GREEN_THRESHOLD:.1f}")
        elif key == ord('s'):
            # 保存当前配置 (只保存 ROI 数据，包含更新的阈值)
            save_roi_data() # 不传递样本，只保存 rois 和 thresholds
    
    # 函数返回前不再需要 return True, 直接退出循环即可
    # 释放资源是在 main 函数的末尾处理
    # return True 

# --- 主程序 ---
def main():
    global current_mode, max_rois, current_resolution_index, exposure_value, brightness_value, cap, rois # 明确声明 rois 为全局变量

    # --- 日志设置 ---
    log_file = 'led_detection.log'
    logging.basicConfig(filename=log_file,
                        level=logging.INFO,
                        format='%(asctime)s.%(msecs)03d - %(levelname)s - %(message)s',
                        datefmt='%Y-%m-%d %H:%M:%S',
                        encoding='utf-8')  # 添加UTF-8编码
    print(f"日志将记录到: {log_file}")
    logging.info("程序启动")
    # --- 日志设置结束 ---

    # 加载配置或使用默认值
    load_camera_settings()
    roi_loaded, _, _ = load_roi_data()

    # 确定初始 ROI 数量
    if not rois: # 如果 roi_data.json 不存在或为空
        try:
            user_input = input(f"请输入需要检测的 LED 灯珠数量 (默认为 {max_rois}): ")
            if user_input.strip():
                max_rois = int(user_input)
                if max_rois <= 0:
                    print("无效的数量，使用默认值 4")
                    max_rois = 4
            # else: 使用全局默认的 max_rois
            print(f"将标定 {max_rois} 个 ROI。")
        except ValueError:
            print("输入无效，使用默认值 4")
            max_rois = 4
    else: # 如果从文件加载了 ROI
        max_rois = len(rois)
        print(f"已加载 {max_rois} 个 ROI。")

    # 打开摄像头
    active_cam_idx = CAMERA_INDEX
    print(f"尝试探测主摄像头 (索引 {CAMERA_INDEX})...")
    temp_cap = cv2.VideoCapture(CAMERA_INDEX)
    if not temp_cap.isOpened():
        print(f"无法打开主摄像头。尝试备用摄像头 (索引 {FALLBACK_CAMERA_INDEX})...")
        active_cam_idx = FALLBACK_CAMERA_INDEX
        if temp_cap: temp_cap.release()
        temp_cap = cv2.VideoCapture(active_cam_idx)
        if not temp_cap.isOpened():
            print("错误：无法打开任何摄像头。请检查摄像头连接和驱动程序。")
            if temp_cap: temp_cap.release()
            return
        else:
             print(f"已成功探测到备用摄像头 (索引 {active_cam_idx})。")
    else:
        print(f"已成功探测到主摄像头 (索引 {active_cam_idx})。")
    if temp_cap: temp_cap.release()

    # 应用初始摄像头设置并获取 cap 对象
    print("应用初始摄像头设置...")
    cap = apply_camera_settings(active_cam_idx)
    if cap is None:
        print("错误：无法应用初始摄像头设置并创建捕获对象。")
        return

    # 确定初始模式
    if roi_loaded:
        print("已加载 ROI 数据。是否直接进入检测模式? (Y/N/S=设置/A=重新标注)")
        user_input = input().strip().lower()
        if user_input == 'y' or user_input == 'yes':
            current_mode = MODE_DETECTION
        elif user_input == 's':
            current_mode = MODE_CAMERA_SETTINGS
        elif user_input == 'a':
             current_mode = MODE_ROI_ANNOTATION
             try:
                 default_rois = len(rois) if rois else max_rois
                 user_input = input(f"请确认或输入需要检测的 LED 灯珠数量 (当前/默认: {default_rois}): ")
                 if user_input.strip():
                     max_rois = int(user_input)
                     if max_rois <= 0:
                         print(f"无效的数量，使用值 {default_rois}")
                         max_rois = default_rois
                 else:
                     max_rois = default_rois
                 print(f"将标定 {max_rois} 个 ROI。")
                 rois = [] # 清空旧 ROI
                 # 清空样本和阈值状态
                 off_state_samples = []
                 on_state_samples = []
                 # 重置阈值到默认值可能更好，或者让用户必须重新分析
                 GRAY_THRESHOLD = 160
                 GREEN_THRESHOLD = 180
             except ValueError:
                 print(f"输入无效，使用值 {default_rois}")
                 max_rois = default_rois
                 rois = []
        else:
            current_mode = MODE_CAMERA_SETTINGS
    else:
        current_mode = MODE_CAMERA_SETTINGS # 没有 ROI 数据，必须先设置

    # 模式切换循环
    running = True
    while running:
        if cap is None or not cap.isOpened():
             print("错误：摄像头对象无效或未打开，退出循环。")
             running = False
             continue

        if current_mode == MODE_CAMERA_SETTINGS:
            # 正确接收 camera_settings_mode 的返回值
            running, updated_cap = camera_settings_mode(cap, active_cam_idx)
            if updated_cap is not None: # 只有在返回有效 cap 时才更新
                cap = updated_cap
            else: # 如果返回 None cap，说明出错了
                running = False
            if not running: # 如果 camera_settings_mode 返回 False 或 cap 为 None，则退出
                 break
        elif current_mode == MODE_ROI_ANNOTATION:
             if max_rois <= 0:
                  max_rois = 4
                  print(f"警告：ROI 数量无效，重置为 {max_rois}")
             running = roi_annotation_mode(cap) # 调用新函数
        elif current_mode == MODE_DETECTION:
            running = detection_mode(cap)
        else:
            print(f"错误：未知模式 {current_mode}")
            running = False

    # 释放资源
    print("释放摄像头资源并关闭窗口...")
    if cap is not None and cap.isOpened():
        cap.release()
    cv2.destroyAllWindows()
    logging.info("程序正常结束") # <--- 添加结束日志
    print("程序结束。")

if __name__ == "__main__":
    main()
