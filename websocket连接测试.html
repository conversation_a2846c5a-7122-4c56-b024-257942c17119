<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket连接测试工具</h1>
        
        <div id="status" class="status info">
            准备测试WebSocket连接...
        </div>
        
        <div>
            <button class="btn-primary" onclick="testConnection()">测试连接</button>
            <button class="btn-secondary" onclick="sendQuery()">发送状态查询</button>
            <button class="btn-secondary" onclick="clearLog()">清空日志</button>
        </div>
        
        <h3>连接日志：</h3>
        <div id="log"></div>
    </div>

    <script>
        let websocket = null;
        let isConnected = false;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function testConnection() {
            if (websocket) {
                websocket.close();
            }
            
            log('开始测试WebSocket连接...');
            updateStatus('正在连接...', 'info');
            
            try {
                websocket = new WebSocket('ws://127.0.0.1:8765');
                
                websocket.onopen = function(event) {
                    isConnected = true;
                    log('✓ WebSocket连接成功！');
                    updateStatus('连接成功', 'success');
                };
                
                websocket.onmessage = function(event) {
                    log(`收到消息: ${event.data}`);
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'connection_confirmed') {
                            log('✓ 收到连接确认消息');
                        }
                    } catch (e) {
                        log(`解析消息失败: ${e}`);
                    }
                };
                
                websocket.onclose = function(event) {
                    isConnected = false;
                    log(`✗ WebSocket连接关闭 (代码: ${event.code}, 原因: ${event.reason})`);
                    updateStatus('连接已关闭', 'error');
                };
                
                websocket.onerror = function(error) {
                    isConnected = false;
                    log(`✗ WebSocket连接错误: ${error}`);
                    updateStatus('连接失败', 'error');
                };
                
            } catch (error) {
                log(`✗ 创建WebSocket连接失败: ${error}`);
                updateStatus('连接失败', 'error');
            }
        }
        
        function sendQuery() {
            if (!websocket || !isConnected) {
                log('❌ 请先建立WebSocket连接');
                return;
            }
            
            const message = {
                type: 'query_status',
                data: {
                    timestamp: new Date().toISOString()
                }
            };
            
            try {
                websocket.send(JSON.stringify(message));
                log('📤 发送状态查询消息');
            } catch (error) {
                log(`❌ 发送消息失败: ${error}`);
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 页面加载完成后自动测试连接
        window.onload = function() {
            log('页面加载完成，准备测试WebSocket连接');
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
