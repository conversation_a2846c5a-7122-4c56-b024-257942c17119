#!/usr/bin/env python3
"""
测试英文提示信息显示
验证异步任务的进度提示已改为英文
"""

import time
from async_task_manager import AsyncTaskManager, TaskType, TaskStatus

def test_english_messages():
    """测试英文提示信息"""
    print("=" * 50)
    print("Testing English Progress Messages")
    print("=" * 50)
    
    # 模拟AppState
    class MockAppState:
        def __init__(self):
            self.led_analysis_state = 'IDLE'
            self.current_analysis_task_id = None
            self.current_cpu_task_id = None
            self.async_task_progress = ""
            self.status_message = ""
            self.last_analysis_result = None
            self.last_special_leds_result = None
    
    app_state = MockAppState()
    manager = AsyncTaskManager()
    manager.start()
    
    try:
        print("\n1. Testing Analysis Task Messages...")
        
        # 模拟分析任务
        app_state.led_analysis_state = 'ANALYZING'
        if app_state.current_analysis_task_id is None:
            # 创建测试日志文件
            test_log = "test_english_log.txt"
            with open(test_log, 'w', encoding='utf-8') as f:
                f.write("2024-01-01 12:00:00.000 - INFO - LED G1: Status=ON\n")
            
            task_id = manager.submit_task(
                TaskType.ANALYZE_LOG,
                {'log_file_path': test_log}
            )
            app_state.current_analysis_task_id = task_id
            app_state.async_task_progress = "Analyzing log file..."
            print(f"✓ Analysis progress message: '{app_state.async_task_progress}'")
        
        # 检查任务状态
        for i in range(3):
            status = manager.get_task_status(app_state.current_analysis_task_id)
            if status == TaskStatus.RUNNING:
                app_state.status_message = "Analyzing log file, please wait..."
                app_state.async_task_progress = "Analysis in progress..."
                print(f"✓ Running status message: '{app_state.status_message}'")
                print(f"✓ Running progress message: '{app_state.async_task_progress}'")
                break
            elif status == TaskStatus.COMPLETED:
                print("✓ Analysis task completed")
                break
            time.sleep(0.5)
        
        print("\n2. Testing CPU Communication Messages...")
        
        # 模拟CPU通信任务
        app_state.led_analysis_state = 'SENDING_RESULT_10'
        app_state.last_analysis_result = 5
        
        if app_state.current_cpu_task_id is None:
            value_to_send = 1
            task_id = manager.submit_task(
                TaskType.SEND_CPU_SIGNAL,
                {'value': value_to_send, 'address': 10}
            )
            app_state.current_cpu_task_id = task_id
            app_state.async_task_progress = "Sending result to CPU address 10..."
            print(f"✓ CPU task progress message: '{app_state.async_task_progress}'")
        
        # 检查CPU任务状态
        for i in range(3):
            status = manager.get_task_status(app_state.current_cpu_task_id)
            if status == TaskStatus.RUNNING:
                app_state.status_message = "Sending analysis result to CPU..."
                print(f"✓ CPU running message: '{app_state.status_message}'")
                break
            elif status == TaskStatus.COMPLETED:
                print("✓ CPU task completed")
                break
            time.sleep(0.5)
        
        print("\n3. Testing Special LED Messages...")
        
        # 模拟特殊LED结果发送
        app_state.led_analysis_state = 'SENDING_RESULT_13'
        app_state.last_special_leds_result = 'GOOD'
        app_state.current_cpu_task_id = None
        
        if app_state.current_cpu_task_id is None:
            value_to_send = 1
            task_id = manager.submit_task(
                TaskType.SEND_CPU_SIGNAL,
                {'value': value_to_send, 'address': 13}
            )
            app_state.current_cpu_task_id = task_id
            app_state.async_task_progress = "Sending special LED result to CPU address 13..."
            print(f"✓ Special LED progress message: '{app_state.async_task_progress}'")
        
        # 检查特殊LED任务状态
        for i in range(3):
            status = manager.get_task_status(app_state.current_cpu_task_id)
            if status == TaskStatus.RUNNING:
                app_state.status_message = "Sending special LED result to CPU..."
                print(f"✓ Special LED running message: '{app_state.status_message}'")
                break
            elif status == TaskStatus.COMPLETED:
                print("✓ Special LED task completed")
                break
            time.sleep(0.5)
        
        print("\n4. Testing Log Completion Signal Messages...")
        
        # 模拟日志完成信号
        app_state.led_analysis_state = 'WAITING_TO_SIGNAL_12'
        app_state.current_cpu_task_id = None
        
        if app_state.current_cpu_task_id is None:
            task_id = manager.submit_task(
                TaskType.SEND_CPU_SIGNAL,
                {'value': 1, 'address': 12}
            )
            app_state.current_cpu_task_id = task_id
            app_state.async_task_progress = "Sending log completion signal to CPU..."
            print(f"✓ Log completion progress message: '{app_state.async_task_progress}'")
        
        # 检查日志完成任务状态
        for i in range(3):
            status = manager.get_task_status(app_state.current_cpu_task_id)
            if status == TaskStatus.RUNNING:
                app_state.status_message = "Sending log completion signal to CPU..."
                print(f"✓ Log completion running message: '{app_state.status_message}'")
                break
            elif status == TaskStatus.COMPLETED:
                print("✓ Log completion task completed")
                break
            time.sleep(0.5)
        
        # 清理测试文件
        import os
        if os.path.exists(test_log):
            os.remove(test_log)
        
        print("\n" + "=" * 50)
        print("✅ All English Messages Test Passed!")
        print("✅ No Chinese characters found in progress messages")
        print("=" * 50)
        
        # 显示所有测试的消息
        print("\nSummary of English Messages:")
        print("- Analyzing log file...")
        print("- Analyzing log file, please wait...")
        print("- Analysis in progress...")
        print("- Sending result to CPU address 10...")
        print("- Sending analysis result to CPU...")
        print("- Sending special LED result to CPU address 13...")
        print("- Sending special LED result to CPU...")
        print("- Sending log completion signal to CPU...")
        
    finally:
        manager.stop()

if __name__ == "__main__":
    test_english_messages()
