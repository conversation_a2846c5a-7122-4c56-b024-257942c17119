import threading
import queue
import time
import logging
from typing import Optional, Dict, Any, Callable
from enum import Enum
from dataclasses import dataclass
from analyze_led_log import analyze_led_cycles
from cpu_communicator import send_value_to_cpu


class TaskType(Enum):
    """异步任务类型枚举"""
    ANALYZE_LOG = "analyze_log"
    SEND_CPU_SIGNAL = "send_cpu_signal"
    CLEAR_LOG_FILE = "clear_log_file"


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class AsyncTask:
    """异步任务数据结构"""
    task_id: str
    task_type: TaskType
    params: Dict[str, Any]
    status: TaskStatus = TaskStatus.PENDING
    result: Any = None
    error: Optional[str] = None
    created_time: float = 0.0
    completed_time: Optional[float] = None


class AsyncTaskManager:
    """异步任务管理器 - 处理耗时操作，避免UI卡顿"""
    
    def __init__(self):
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.tasks = {}  # task_id -> AsyncTask
        self.worker_thread = None
        self.running = False
        self.task_counter = 0
        self._lock = threading.Lock()
        
    def start(self):
        """启动异步任务管理器"""
        if self.running:
            return
            
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        logging.info("AsyncTaskManager started")
        
    def stop(self):
        """停止异步任务管理器"""
        if not self.running:
            return
            
        self.running = False
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5.0)
        logging.info("AsyncTaskManager stopped")
        
    def submit_task(self, task_type: TaskType, params: Dict[str, Any]) -> str:
        """提交异步任务"""
        with self._lock:
            self.task_counter += 1
            task_id = f"{task_type.value}_{self.task_counter}_{int(time.time())}"
            
        task = AsyncTask(
            task_id=task_id,
            task_type=task_type,
            params=params,
            created_time=time.time()
        )
        
        self.tasks[task_id] = task
        self.task_queue.put(task)
        
        logging.info(f"Submitted async task: {task_id} ({task_type.value})")
        return task_id
        
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        task = self.tasks.get(task_id)
        return task.status if task else None
        
    def get_task_result(self, task_id: str) -> Optional[Any]:
        """获取任务结果"""
        task = self.tasks.get(task_id)
        if task and task.status == TaskStatus.COMPLETED:
            return task.result
        return None
        
    def get_task_error(self, task_id: str) -> Optional[str]:
        """获取任务错误信息"""
        task = self.tasks.get(task_id)
        if task and task.status == TaskStatus.FAILED:
            return task.error
        return None
        
    def check_completed_tasks(self) -> list:
        """检查已完成的任务，返回任务ID列表"""
        completed_tasks = []
        try:
            while True:
                task_id = self.result_queue.get_nowait()
                completed_tasks.append(task_id)
        except queue.Empty:
            pass
        return completed_tasks
        
    def cleanup_old_tasks(self, max_age_seconds: float = 300):
        """清理旧任务（默认5分钟）"""
        current_time = time.time()
        to_remove = []
        
        for task_id, task in self.tasks.items():
            if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED] and 
                current_time - task.created_time > max_age_seconds):
                to_remove.append(task_id)
                
        for task_id in to_remove:
            del self.tasks[task_id]
            
        if to_remove:
            logging.info(f"Cleaned up {len(to_remove)} old tasks")
            
    def _worker_loop(self):
        """工作线程主循环"""
        logging.info("AsyncTaskManager worker thread started")
        
        while self.running:
            try:
                # 等待任务，超时1秒以便检查running状态
                task = self.task_queue.get(timeout=1.0)
                self._execute_task(task)
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Worker thread error: {e}")
                
        logging.info("AsyncTaskManager worker thread stopped")
        
    def _execute_task(self, task: AsyncTask):
        """执行具体任务"""
        try:
            task.status = TaskStatus.RUNNING
            logging.info(f"Executing task: {task.task_id} ({task.task_type.value})")
            
            if task.task_type == TaskType.ANALYZE_LOG:
                result = self._execute_analyze_log(task.params)
            elif task.task_type == TaskType.SEND_CPU_SIGNAL:
                result = self._execute_send_cpu_signal(task.params)
            elif task.task_type == TaskType.CLEAR_LOG_FILE:
                result = self._execute_clear_log_file(task.params)
            else:
                raise ValueError(f"Unknown task type: {task.task_type}")
                
            task.result = result
            task.status = TaskStatus.COMPLETED
            task.completed_time = time.time()
            
            logging.info(f"Task completed: {task.task_id}")
            
        except Exception as e:
            task.error = str(e)
            task.status = TaskStatus.FAILED
            task.completed_time = time.time()
            logging.error(f"Task failed: {task.task_id}, error: {e}")
            
        finally:
            # 通知主线程任务完成
            self.result_queue.put(task.task_id)
            
    def _execute_analyze_log(self, params: Dict[str, Any]) -> Any:
        """执行日志分析任务"""
        log_file_path = params.get('log_file_path')
        if not log_file_path:
            raise ValueError("Missing log_file_path parameter")
            
        return analyze_led_cycles(log_file_path)
        
    def _execute_send_cpu_signal(self, params: Dict[str, Any]) -> bool:
        """执行CPU信号发送任务"""
        value = params.get('value')
        address = params.get('address')
        
        if value is None or address is None:
            raise ValueError("Missing value or address parameter")
            
        return send_value_to_cpu(value=value, address=address)
        
    def _execute_clear_log_file(self, params: Dict[str, Any]) -> bool:
        """执行日志文件清理任务"""
        log_file_path = params.get('log_file_path')
        if not log_file_path:
            raise ValueError("Missing log_file_path parameter")
            
        try:
            with open(log_file_path, 'w', encoding='utf-8') as f:
                pass
            return True
        except Exception as e:
            logging.error(f"Failed to clear log file {log_file_path}: {e}")
            return False


# 全局实例
_task_manager = None


def get_task_manager() -> AsyncTaskManager:
    """获取全局任务管理器实例"""
    global _task_manager
    if _task_manager is None:
        _task_manager = AsyncTaskManager()
    return _task_manager


def initialize_task_manager():
    """初始化并启动任务管理器"""
    manager = get_task_manager()
    manager.start()
    return manager


def shutdown_task_manager():
    """关闭任务管理器"""
    global _task_manager
    if _task_manager:
        _task_manager.stop()
        _task_manager = None
