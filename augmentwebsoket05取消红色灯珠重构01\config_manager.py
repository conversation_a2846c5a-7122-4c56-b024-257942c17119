import json
import os
import numpy as np
import base64
import cv2
import logging
from datetime import datetime
from app_state import AppState # 导入 AppState 类
from constants import * # 导入所有常量

def samples_to_list(samples, max_rois):
    """将样本数据 (可能是 NumPy 数组列表) 转换为纯列表以便 JSON 序列化"""
    full_samples = [[] for _ in range(max_rois)]
    if samples and isinstance(samples, list):
        for i in range(min(len(samples), max_rois)):
            if samples[i] and isinstance(samples[i], list):
                valid_inner_samples = []
                for s in samples[i]:
                    # 检查是否是 numpy 数组且形状正确
                    if isinstance(s, np.ndarray) and s.shape == (3,):
                         valid_inner_samples.append(s.tolist()) # 转换为列表
                    # 检查是否已经是列表且长度正确 (兼容旧格式或未转换的数据)
                    elif isinstance(s, (list, tuple)) and len(s) == 3:
                         try:
                              # 确保可以转换为浮点数
                              float_s = [float(val) for val in s]
                              valid_inner_samples.append(float_s)
                         except (ValueError, TypeError):
                              print(f"警告: 在 ROI {i} 中跳过无效样本数据 {s}")
                    # else: # 跳过其他无效格式
                    #     print(f"警告: 在 ROI {i} 中跳过非列表或 NumPy 样本 {s}")

                full_samples[i] = valid_inner_samples
            # else: full_samples[i] is already []
    return full_samples

def list_to_samples(lst, expected_len):
    """将从 JSON 加载的列表转换回样本列表 (包含 NumPy 数组)"""
    result_samples = [[] for _ in range(expected_len)]
    if not isinstance(lst, list):
        print(f"警告: 样本需要是一个列表，但得到 {type(lst)}。将重置样本。")
        return result_samples

    loaded_count = len(lst)
    # 调整列表长度以匹配期望长度
    if loaded_count < expected_len:
        lst.extend([[] for _ in range(expected_len - loaded_count)])
    elif loaded_count > expected_len:
        lst = lst[:expected_len]
        if loaded_count > expected_len:
             print(f"警告: 加载的样本数 ({loaded_count}) 多于预期的 ROI 数 ({expected_len})。已截断。")


    for i in range(expected_len):
        roi_samples = lst[i]
        if isinstance(roi_samples, list):
            valid_inner_samples = []
            for s in roi_samples:
                if isinstance(s, list) and len(s) == 3:
                    try:
                        # 尝试转换为 float 再转 numpy array
                        float_s = [float(val) for val in s]
                        valid_inner_samples.append(np.array(float_s, dtype=np.float64)) # 使用 float64 更安全
                    except (ValueError, TypeError):
                        # print(f"警告: 跳过 ROI {i} 中无效的样本数据 {s}") # 减少冗余输出
                        pass
                # else: print(f"警告: 跳过 ROI {i} 中非列表或长度错误的样本 {s}")
            result_samples[i] = valid_inner_samples
        # else: print(f"警告: ROI {i} 的样本期望是列表，但得到 {type(roi_samples)}。保持为空。")

    return result_samples

def save_config(app_state: AppState):
    """将 AppState 中的配置保存到 JSON 文件"""
    # 将 NumPy 数组样本转换为列表
    off_samples_list = samples_to_list(app_state.led_off_state_samples, app_state.led_max_rois)
    on_samples_list = samples_to_list(app_state.led_on_state_samples, app_state.led_max_rois)

    config_data = {
        "version": "1.1", # 版本号
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "camera_settings": {
            "resolution_index": app_state.current_resolution_index,
            "exposure": app_state.exposure_value,
            "brightness": app_state.brightness_value,
        },
        "led_settings": {
            "num_green": app_state.led_num_green,
            "num_red": app_state.led_num_red,
            # 确保 ROI 是整数列表，处理 None
            "rois": [[int(x) for x in roi] if roi else None for roi in app_state.led_rois],
            "off_samples": off_samples_list,
            "on_samples": on_samples_list,
            "gray_threshold_green": app_state.led_gray_threshold_green,
            "green_threshold": app_state.led_green_threshold,
            "gray_threshold_red": app_state.led_gray_threshold_red,
            "red_threshold": app_state.led_red_threshold,
            "fixed_template_size": app_state.fixed_template_size,  # 固定模板大小
            # G33特殊LED阈值
            "gray_threshold_g33": app_state.led_gray_threshold_g33,
            "green_threshold_g33": app_state.led_green_threshold_g33,
        },
        "digit_settings": {
            "digit_rois": [[int(x) for x in roi] if roi else None for roi in app_state.digit_rois],
            "segment_rois": [[[int(x) for x in seg] if seg else None for seg in digit] for digit in app_state.digit_segment_rois],
            "brightness_threshold": app_state.digit_brightness_threshold,
        },

        # 基准点对齐配置
        "base_alignment": {
            "enabled": app_state.alignment_enabled,
            "base_points": app_state.base_points,
            "original_base_points": app_state.original_base_points,
            "template_size": app_state.base_template_size,
            "match_threshold": app_state.base_match_threshold,
            # 基准点模板图像(base64编码)
            "base_templates_b64": [
                base64.b64encode(cv2.imencode('.png', tmpl)[1]).decode('utf-8')
                if tmpl is not None else None
                for tmpl in app_state.base_templates
            ]
        },

        # 原始ROI坐标（用于偏移计算）
        "original_rois": {
            "led_rois": [[int(x) for x in roi] if roi else None for roi in app_state.original_led_rois],
            "digit_rois": [[int(x) for x in roi] if roi else None for roi in app_state.original_digit_rois],
            "digit_segment_rois": [[[int(x) for x in seg] if seg else None for seg in digit] for digit in app_state.original_digit_segment_rois]
        },

        # 分析设置
        "analysis_settings": {
            "logging_duration": app_state.logging_duration,
        }
    }

    try:
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config_data, f, indent=4)
        print(f"配置已保存到 {CONFIG_FILE}")
        return True
    except Exception as e:
        print(f"保存配置失败: {e}")
        return False


def save_led_samples_and_thresholds_only(app_state: AppState):
    """仅更新LED样本数据和阈值，保留ROI坐标等其他配置不变"""
    if not os.path.exists(CONFIG_FILE):
        print(f"配置文件 {CONFIG_FILE} 不存在，无法进行增量更新。请先完整保存配置。")
        return False

    try:
        # 创建备份
        backup_file = CONFIG_FILE + '.bak'
        import shutil
        shutil.copy2(CONFIG_FILE, backup_file)
        print(f"已创建配置备份: {backup_file}")

        # 读取现有配置
        with open(CONFIG_FILE, 'r') as f:
            config_data = json.load(f)

        # 将 NumPy 数组样本转换为列表
        off_samples_list = samples_to_list(app_state.led_off_state_samples, app_state.led_max_rois)
        on_samples_list = samples_to_list(app_state.led_on_state_samples, app_state.led_max_rois)

        # 只更新LED样本和阈值相关字段
        if "led_settings" not in config_data:
            config_data["led_settings"] = {}

        config_data["led_settings"]["off_samples"] = off_samples_list
        config_data["led_settings"]["on_samples"] = on_samples_list
        config_data["led_settings"]["gray_threshold_green"] = app_state.led_gray_threshold_green
        config_data["led_settings"]["green_threshold"] = app_state.led_green_threshold
        config_data["led_settings"]["gray_threshold_red"] = app_state.led_gray_threshold_red
        config_data["led_settings"]["red_threshold"] = app_state.led_red_threshold
        # G33特殊LED阈值
        config_data["led_settings"]["gray_threshold_g33"] = app_state.led_gray_threshold_g33
        config_data["led_settings"]["green_threshold_g33"] = app_state.led_green_threshold_g33

        # 更新时间戳
        config_data["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 使用临时文件+原子替换，防止写入过程中断电导致文件损坏
        temp_file = CONFIG_FILE + '.tmp'
        with open(temp_file, 'w') as f:
            json.dump(config_data, f, indent=4)

        # 原子替换
        if os.name == 'nt':  # Windows
            os.replace(temp_file, CONFIG_FILE)
        else:  # Unix/Linux
            os.rename(temp_file, CONFIG_FILE)

        print(f"LED样本数据和阈值已更新到 {CONFIG_FILE}")
        print("✓ ROI坐标等其他配置保持不变")
        return True

    except Exception as e:
        print(f"更新LED样本数据失败: {e}")
        # 尝试恢复备份
        if os.path.exists(backup_file):
            try:
                shutil.copy2(backup_file, CONFIG_FILE)
                print(f"已从备份恢复配置文件")
            except:
                print(f"警告：无法恢复备份，请手动检查配置文件")
        return False




def load_config(app_state: AppState):
    """从 JSON 文件加载配置到 AppState 对象"""
    if not os.path.exists(CONFIG_FILE):
        print(f"配置文件 {CONFIG_FILE} 未找到。")
        led_num_green_temp = app_state.led_num_green
        led_num_red_temp = app_state.led_num_red
        try:
            user_input_green = input(f"请输入要检测的绿色 LED 数量 (默认 {led_num_green_temp}): ")
            num_g = led_num_green_temp
            if user_input_green.strip():
                num_g = int(user_input_green)

            user_input_red = input(f"请输入要检测的红色 LED 数量 (默认 {led_num_red_temp}): ")
            num_r = led_num_red_temp
            if user_input_red.strip():
                num_r = int(user_input_red)

            # 更新 AppState 中的 LED 数量及相关列表
            app_state.update_led_counts(num_g, num_r)

            if app_state.led_max_rois == 0:
                 print("警告: LED 总数为零。检测可能无法正常工作。")
            else:
                 print(f"将校准 {app_state.led_num_green} 个绿色和 {app_state.led_num_red} 个红色 LED (总共: {app_state.led_max_rois}).")

            # 配置日志记录时间
            try:
                user_input_duration = input(f"请输入采样记录时间（秒，默认 {DEFAULT_LOGGING_DURATION}）: ")
                if user_input_duration.strip():
                    duration = float(user_input_duration)
                    if 5 <= duration <= 300:  # 限制合理范围
                        app_state.logging_duration = duration
                    else:
                        print("记录时间应在5-300秒之间，使用默认值")
                        app_state.logging_duration = DEFAULT_LOGGING_DURATION
                else:
                    app_state.logging_duration = DEFAULT_LOGGING_DURATION
                print(f"日志记录时间设置为: {app_state.logging_duration}秒")
            except ValueError:
                print("输入无效，使用默认记录时间")
                app_state.logging_duration = DEFAULT_LOGGING_DURATION

        except ValueError:
            print(f"输入无效，使用默认值: {led_num_green_temp} 绿色, {led_num_red_temp} 红色。")
            # 恢复默认值并更新 AppState
            app_state.update_led_counts(led_num_green_temp, led_num_red_temp)
            # 使用默认日志记录时间
            app_state.logging_duration = DEFAULT_LOGGING_DURATION

        # 配置文件不存在时，强制进入设置/校准模式
        app_state.current_mode = MODE_CAMERA_SETTINGS # 或 MODE_CALIBRATION? Settings 更合适
        app_state.current_calib_state = CALIB_STATE_START
        # 确保列表为空或长度正确 (update_led_counts 已处理大部分)
        # 但样本列表需要明确初始化为空列表的列表
        app_state.led_off_state_samples = [[] for _ in range(app_state.led_max_rois)]
        app_state.led_on_state_samples = [[] for _ in range(app_state.led_max_rois)]

        print("请先进行摄像头设置和校准。")
        return False # 表示加载失败或需要初始化

    try:
        with open(CONFIG_FILE, 'r') as f:
            config_data = json.load(f)

        # 加载摄像头设置
        cam_settings = config_data.get("camera_settings", {})
        loaded_res_idx = cam_settings.get("resolution_index", DEFAULT_RESOLUTION_INDEX)
        if not (0 <= loaded_res_idx < len(RESOLUTION_PRESETS)):
             print(f"警告: 配置文件中的分辨率索引 {loaded_res_idx} 无效，使用默认值 {DEFAULT_RESOLUTION_INDEX}")
             app_state.current_resolution_index = DEFAULT_RESOLUTION_INDEX
        else:
             app_state.current_resolution_index = loaded_res_idx
        app_state.exposure_value = float(cam_settings.get("exposure", DEFAULT_EXPOSURE))
        app_state.brightness_value = float(cam_settings.get("brightness", DEFAULT_BRIGHTNESS))
        print("摄像头设置已加载。")

        # 加载 LED 设置
        led_settings = config_data.get("led_settings", {})
        # 加载 LED 数量
        led_num_green_loaded = led_settings.get("num_green", DEFAULT_NUM_GREEN_LEDS)
        led_num_red_loaded = led_settings.get("num_red", DEFAULT_NUM_RED_LEDS)
        num_g = led_num_green_loaded if isinstance(led_num_green_loaded, int) and led_num_green_loaded >= 0 else DEFAULT_NUM_GREEN_LEDS
        num_r = led_num_red_loaded if isinstance(led_num_red_loaded, int) and led_num_red_loaded >= 0 else DEFAULT_NUM_RED_LEDS

        # 更新 AppState 中的 LED 数量及相关列表结构
        app_state.update_led_counts(num_g, num_r)
        print(f"LED 数量已加载: {app_state.led_num_green} 绿色, {app_state.led_num_red} 红色 (总共: {app_state.led_max_rois})")


        # 加载 LED ROIs
        loaded_rois_raw = led_settings.get("rois", [])
        # 确保加载的 ROI 列表与 max_rois 匹配
        if isinstance(loaded_rois_raw, list):
            # 截断或填充 None 以匹配长度
            if len(loaded_rois_raw) < app_state.led_max_rois:
                loaded_rois_raw.extend([None] * (app_state.led_max_rois - len(loaded_rois_raw)))
            elif len(loaded_rois_raw) > app_state.led_max_rois:
                loaded_rois_raw = loaded_rois_raw[:app_state.led_max_rois]
                print(f"警告: 配置文件中的 LED ROI 数量 ({len(led_settings.get('rois', []))}) 多于预期 ({app_state.led_max_rois})。已截断。")

            # 转换有效的 ROI 定义
            app_state.led_rois = [tuple(roi) if isinstance(roi, list) and len(roi) == 4 else None for roi in loaded_rois_raw]
        else:
            print("警告: 配置文件中的 LED ROI 格式无效，将使用空列表。")
            app_state.led_rois = [None] * app_state.led_max_rois


        # 加载样本
        loaded_off_samples = led_settings.get("off_samples", [])
        app_state.led_off_state_samples = list_to_samples(loaded_off_samples, app_state.led_max_rois)

        loaded_on_samples = led_settings.get("on_samples", [])
        app_state.led_on_state_samples = list_to_samples(loaded_on_samples, app_state.led_max_rois)

        # 加载阈值 - 兼容旧格式
        app_state.led_gray_threshold_green = float(led_settings.get("gray_threshold_green", led_settings.get("gray_threshold", DEFAULT_LED_GRAY_THRESHOLD_GREEN)))
        app_state.led_green_threshold = float(led_settings.get("green_threshold", DEFAULT_LED_GREEN_THRESHOLD))
        app_state.led_gray_threshold_red = float(led_settings.get("gray_threshold_red", DEFAULT_LED_GRAY_THRESHOLD_RED))
        app_state.led_red_threshold = float(led_settings.get("red_threshold", DEFAULT_LED_RED_THRESHOLD))

        # 加载G33特殊LED阈值
        app_state.led_gray_threshold_g33 = float(led_settings.get("gray_threshold_g33", DEFAULT_LED_GRAY_THRESHOLD_G33))
        app_state.led_green_threshold_g33 = float(led_settings.get("green_threshold_g33", DEFAULT_LED_GREEN_THRESHOLD_G33))

        # 加载固定模板大小
        app_state.fixed_template_size = int(led_settings.get("fixed_template_size", DEFAULT_FIXED_TEMPLATE_SIZE))

        print(f"LED 设置已加载 ({sum(1 for r in app_state.led_rois if r)}/{app_state.led_max_rois} 个 ROI 已定义)。")


        # 加载数码管设置
        digit_settings = config_data.get("digit_settings", {})
        digit_rois_raw = digit_settings.get("digit_rois", [None] * NUM_DIGITS)
        # 确保长度正确
        if isinstance(digit_rois_raw, list):
             if len(digit_rois_raw) < NUM_DIGITS: digit_rois_raw.extend([None]*(NUM_DIGITS-len(digit_rois_raw)))
             digit_rois_raw = digit_rois_raw[:NUM_DIGITS]
             app_state.digit_rois = [tuple(roi) if isinstance(roi, list) and len(roi) == 4 else None for roi in digit_rois_raw]
        else:
             app_state.digit_rois = [None] * NUM_DIGITS

        digit_segment_rois_loaded = digit_settings.get("segment_rois", [[None]*NUM_SEGMENTS_PER_DIGIT for _ in range(NUM_DIGITS)])
        # 验证段 ROI 结构
        valid_segment_rois = [[None]*NUM_SEGMENTS_PER_DIGIT for _ in range(NUM_DIGITS)]
        if isinstance(digit_segment_rois_loaded, list) and len(digit_segment_rois_loaded) == NUM_DIGITS:
            for d_idx in range(NUM_DIGITS):
                 if isinstance(digit_segment_rois_loaded[d_idx], list) and len(digit_segment_rois_loaded[d_idx]) == NUM_SEGMENTS_PER_DIGIT:
                      for s_idx in range(NUM_SEGMENTS_PER_DIGIT):
                           seg = digit_segment_rois_loaded[d_idx][s_idx]
                           if isinstance(seg, list) and len(seg) == 4:
                                try:
                                     # 确保所有元素都是整数
                                     valid_segment_rois[d_idx][s_idx] = tuple(int(x) for x in seg)
                                except (ValueError, TypeError): pass # 转换失败则保持 None
                 # else: print(f"警告: 数字 {d_idx+1} 的段 ROI 结构无效")
        # else: print("警告: 顶层段 ROI 结构无效")
        app_state.digit_segment_rois = valid_segment_rois

        app_state.digit_brightness_threshold = float(digit_settings.get("brightness_threshold", DEFAULT_DIGIT_BRIGHTNESS_THRESHOLD))
        print("数码管设置已加载。")

        # 加载基准点对齐配置
        base_alignment = config_data.get("base_alignment", {})
        app_state.alignment_enabled = base_alignment.get("enabled", True)
        app_state.base_template_size = base_alignment.get("template_size", BASE_TEMPLATE_SIZE)
        app_state.base_match_threshold = base_alignment.get("match_threshold", BASE_MATCH_THRESHOLD)

        # 加载基准点坐标
        app_state.base_points = base_alignment.get("base_points", [None, None])
        app_state.original_base_points = base_alignment.get("original_base_points", [None, None])

        # 加载基准点模板图像
        base_templates_b64 = base_alignment.get("base_templates_b64", [None, None])
        app_state.base_templates = [None, None]
        template_load_success = 0

        for i, tmpl_b64 in enumerate(base_templates_b64):
            if tmpl_b64:
                try:
                    # 验证base64字符串
                    if not isinstance(tmpl_b64, str) or len(tmpl_b64) < 100:
                        print(f"警告: 基准点模板 {i+1} base64数据无效")
                        continue

                    # 解码base64并转换为OpenCV图像
                    img_data = base64.b64decode(tmpl_b64)
                    if len(img_data) == 0:
                        print(f"警告: 基准点模板 {i+1} 解码后数据为空")
                        continue

                    img_array = np.frombuffer(img_data, np.uint8)
                    template = cv2.imdecode(img_array, cv2.IMREAD_COLOR)

                    if template is not None and template.size > 0:
                        # 验证模板尺寸合理性
                        h, w = template.shape[:2]
                        if 10 <= w <= 100 and 10 <= h <= 100:
                            app_state.base_templates[i] = template
                            template_load_success += 1
                            print(f"✓ 基准点模板 {i+1} 已加载 ({w}x{h})")
                        else:
                            print(f"警告: 基准点模板 {i+1} 尺寸异常 ({w}x{h})")
                    else:
                        print(f"警告: 基准点模板 {i+1} 解码失败")

                except Exception as e:
                    print(f"❌ 基准点模板 {i+1} 加载失败: {e}")
                    logging.error(f"基准点模板加载异常: {e}")

        # 检查基准点配置完整性
        base_points_complete = (app_state.base_points[0] is not None and
                               app_state.base_points[1] is not None and
                               app_state.original_base_points[0] is not None and
                               app_state.original_base_points[1] is not None)

        # 检查模板加载完整性
        if template_load_success == 2 and base_points_complete:
            print("✓ 所有基准点模板和坐标加载成功")
        elif template_load_success == 1:
            print("⚠ 只有一个基准点模板加载成功，基准点对齐功能将被禁用")
            app_state.alignment_enabled = False
        elif not base_points_complete:
            print("⚠ 基准点坐标数据不完整，基准点对齐功能将被禁用")
            app_state.alignment_enabled = False
        else:
            print("❌ 基准点模板加载失败，基准点对齐功能将被禁用")
            app_state.alignment_enabled = False

        # 加载原始ROI坐标
        original_rois = config_data.get("original_rois", {})

        # 加载原始LED ROI
        original_led_rois = original_rois.get("led_rois", [None] * app_state.led_max_rois)
        app_state.original_led_rois = [None] * app_state.led_max_rois
        for i, roi in enumerate(original_led_rois[:app_state.led_max_rois]):
            if roi and isinstance(roi, list) and len(roi) == 4:
                try:
                    app_state.original_led_rois[i] = tuple(int(x) for x in roi)
                except (ValueError, TypeError):
                    pass

        # 加载原始数码管ROI
        original_digit_rois = original_rois.get("digit_rois", [None] * NUM_DIGITS)
        app_state.original_digit_rois = [None] * NUM_DIGITS
        for i, roi in enumerate(original_digit_rois[:NUM_DIGITS]):
            if roi and isinstance(roi, list) and len(roi) == 4:
                try:
                    app_state.original_digit_rois[i] = tuple(int(x) for x in roi)
                except (ValueError, TypeError):
                    pass

        # 加载原始数码管段ROI
        original_segment_rois = original_rois.get("digit_segment_rois", [[None]*NUM_SEGMENTS_PER_DIGIT for _ in range(NUM_DIGITS)])
        app_state.original_digit_segment_rois = [[None]*NUM_SEGMENTS_PER_DIGIT for _ in range(NUM_DIGITS)]
        if isinstance(original_segment_rois, list) and len(original_segment_rois) == NUM_DIGITS:
            for d_idx in range(NUM_DIGITS):
                if isinstance(original_segment_rois[d_idx], list) and len(original_segment_rois[d_idx]) == NUM_SEGMENTS_PER_DIGIT:
                    for s_idx in range(NUM_SEGMENTS_PER_DIGIT):
                        seg = original_segment_rois[d_idx][s_idx]
                        if isinstance(seg, list) and len(seg) == 4:
                            try:
                                app_state.original_digit_segment_rois[d_idx][s_idx] = tuple(int(x) for x in seg)
                            except (ValueError, TypeError):
                                pass

        alignment_status = "启用" if app_state.alignment_enabled else "禁用"
        base_points_status = "已配置" if all(app_state.base_points) else "未配置"
        print(f"基准点对齐设置已加载: {alignment_status}, 基准点: {base_points_status}")

        # 加载分析设置
        analysis_settings = config_data.get("analysis_settings", {})
        app_state.logging_duration = float(analysis_settings.get("logging_duration", DEFAULT_LOGGING_DURATION))
        print(f"分析设置已加载: 记录时间 {app_state.logging_duration}s")

        print(f"配置加载成功 (保存于 {config_data.get('timestamp', '未知')})")

        # 询问用户下一步操作
        print("配置已加载。是否直接进入检测模式？ (Y/N/C=重新校准)")
        user_input = input().strip().lower()
        if user_input in ['y', 'yes']:
            # 检查校准数据是否完整
            num_defined_led_rois = sum(1 for r in app_state.led_rois if r)
            # 检查所有 *定义的* LED ROI 是否都有 OFF 和 ON 样本
            led_samples_ok_for_defined = all(
                (app_state.led_off_state_samples[i] and app_state.led_on_state_samples[i])
                for i, r in enumerate(app_state.led_rois) if r
            )
            # 完整的 LED 校准意味着所有预期 ROI 都已定义且都有样本
            led_calib_ok = (app_state.led_max_rois == 0) or \
                           (num_defined_led_rois == app_state.led_max_rois and led_samples_ok_for_defined)

            # 检查所有数码管 ROI 和所有段 ROI 是否都已定义
            digit_calib_ok = all(r for r in app_state.digit_rois) and \
                             all(all(s for s in d if s) for d in app_state.digit_segment_rois)

            if led_calib_ok and digit_calib_ok:
                 print("校准数据完整，进入检测模式。")
                 app_state.current_mode = MODE_DETECTION
            else:
                 missing_parts = []
                 if not led_calib_ok:
                      missing_parts.append(f"LEDs (ROIs: {num_defined_led_rois}/{app_state.led_max_rois}, Samples OK: {led_samples_ok_for_defined})")
                 if not digit_calib_ok:
                      digits_ok = sum(1 for r in app_state.digit_rois if r)
                      segments_ok = sum(sum(1 for s in d if s) for d in app_state.digit_segment_rois)
                      missing_parts.append(f"Digits (ROIs: {digits_ok}/{NUM_DIGITS}, Segments: {segments_ok}/{NUM_DIGITS * NUM_SEGMENTS_PER_DIGIT})")
                 print(f"警告: 校准数据不完整: {', '.join(missing_parts)}，请重新校准。")
                 app_state.current_mode = MODE_CALIBRATION
                 app_state.current_calib_state = CALIB_STATE_START
        elif user_input == 'c':
             print("进入校准模式...")
             app_state.current_mode = MODE_CALIBRATION
             app_state.current_calib_state = CALIB_STATE_START
        else: # 默认或 N
             print("进入摄像头设置模式...")
             app_state.current_mode = MODE_CAMERA_SETTINGS

        return True

    except Exception as e:
        print(f"加载配置文件失败: {e}")
        print("将使用默认设置，请检查摄像头设置并进行校准。")
        # 出错时重置为默认状态，但保留用户输入的 LED 数量（如果在文件不存在时输入过）
        app_state.current_resolution_index = DEFAULT_RESOLUTION_INDEX
        app_state.exposure_value = DEFAULT_EXPOSURE
        app_state.brightness_value = DEFAULT_BRIGHTNESS
        # LED 数量可能在上面文件不存在时已设置，这里只重置 ROI、样本和阈值
        app_state.led_rois = [None] * app_state.led_max_rois
        app_state.led_off_state_samples = [[] for _ in range(app_state.led_max_rois)]
        app_state.led_on_state_samples = [[] for _ in range(app_state.led_max_rois)]
        app_state.led_gray_threshold_green = DEFAULT_LED_GRAY_THRESHOLD_GREEN
        app_state.led_green_threshold = DEFAULT_LED_GREEN_THRESHOLD
        app_state.led_gray_threshold_red = DEFAULT_LED_GRAY_THRESHOLD_RED
        app_state.led_red_threshold = DEFAULT_LED_RED_THRESHOLD
        # 重置数码管
        app_state.digit_rois = [None] * NUM_DIGITS
        app_state.digit_segment_rois = [[None] * NUM_SEGMENTS_PER_DIGIT for _ in range(NUM_DIGITS)]
        app_state.digit_brightness_threshold = DEFAULT_DIGIT_BRIGHTNESS_THRESHOLD

        app_state.current_mode = MODE_CAMERA_SETTINGS # 强制进入设置
        app_state.current_calib_state = CALIB_STATE_START
        return False # 表示加载失败
