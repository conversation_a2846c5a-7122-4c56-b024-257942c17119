# 视觉检测WebSocket实时通信集成说明

## 🎯 功能概述

本次集成实现了**本地视觉检测程序**与**网页端CPU控制器**之间的实时通信，当本地程序检测到"88"开始数据采集时，网页端会实时显示倒计时进度。

## 🏗️ 架构设计

```
用户本地环境：
┌─────────────────────────────────────────────────────────┐
│  视觉检测程序 ←→ WebSocket服务器(127.0.0.1:8765)        │
│       ↑                    ↓                           │
│   检测到"88"         实时倒计时推送                      │
│   开始50秒采集              ↓                           │
│                    用户浏览器页面                        │
│                 (CPU控制器Vue组件)                       │
└─────────────────────────────────────────────────────────┘
```

## 📋 新增文件

1. **websocket_server.py** - WebSocket服务器核心模块
2. **test_websocket.py** - WebSocket功能测试脚本
3. **requirements_websocket.txt** - WebSocket依赖包
4. **WebSocket集成说明.md** - 本说明文档

## 🔧 修改的文件

### 后端修改
- **main.py** - 添加WebSocket服务器启动和关闭
- **ui_handler.py** - 集成WebSocket状态推送

### 前端修改
- **static/page_js_css/CPUControllerVue.js** - 添加WebSocket连接和倒计时显示

## 🚀 使用流程

### 1. 安装依赖
```bash
pip install -r requirements_websocket.txt
```

### 2. 启动视觉检测程序
```bash
python main.py
```
程序启动时会自动启动WebSocket服务器在端口8765

### 3. 打开网页
访问CPU控制器Vue页面，页面会自动尝试连接本地WebSocket服务器

### 4. 开始检测
- **方式一**：在本地视觉检测程序中，当检测到"88"时自动开始50秒倒计时
- **方式二**：在网页上点击"视觉检测"按钮，会先查询本地程序状态

## 📊 消息协议

### 浏览器 → 本地程序
```json
{
  "type": "query_status",
  "data": {
    "timestamp": "2025-01-20 10:30:00"
  }
}
```

### 本地程序 → 浏览器
```json
{
  "type": "countdown_update",
  "data": {
    "state": "LOGGING",
    "countdown": 45,
    "total_duration": 50,
    "progress": 10,
    "message": "正在记录LED状态数据...",
    "is_active": true,
    "start_time": "10:30:00"
  }
}
```

## 🎨 界面效果

当本地程序开始倒计时时，网页会显示：
- **倒计时圆圈**：显示剩余秒数
- **进度条**：显示完成百分比
- **状态消息**：显示当前操作
- **连接状态**：显示与本地程序的连接状态

## 🔍 测试方法

### 测试WebSocket服务器
```bash
python test_websocket.py
```

### 测试WebSocket客户端
```bash
python test_websocket.py client
```

## 🛠️ 故障排除

### 1. 连接失败
- 确保视觉检测程序已启动
- 检查端口8765是否被占用
- 确认防火墙设置

### 2. 消息不同步
- 检查WebSocket连接状态
- 查看浏览器控制台错误信息
- 检查本地程序日志

### 3. 倒计时不显示
- 确认产品类型支持视觉检测
- 检查Vue组件是否正确加载
- 验证CSS样式是否生效

## 📈 性能特点

- **零延迟**：本地通信，毫秒级响应
- **自动重连**：网页会自动重连断开的连接
- **状态同步**：实时同步倒计时状态
- **错误处理**：完善的异常处理机制

## 🔄 状态机

```
IDLE (空闲) → LOGGING (记录中) → ANALYZING (分析中) → IDLE (完成)
     ↑                                                    ↓
     └────────────────── 循环 ──────────────────────────┘
```

## 💡 扩展功能

未来可以扩展的功能：
1. **双向控制**：网页端控制本地程序
2. **多用户支持**：支持多个用户同时使用
3. **历史记录**：保存倒计时历史
4. **自定义配置**：可配置倒计时时长

## 📞 技术支持

如有问题，请检查：
1. Python版本 >= 3.7
2. websockets包版本 >= 11.0.3
3. 浏览器支持WebSocket
4. 网络连接正常
