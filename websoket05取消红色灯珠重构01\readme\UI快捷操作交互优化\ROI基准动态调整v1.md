# ROI基准动态调整方案 v1.0

## 📋 项目概述

### 问题描述
当前视觉检测系统使用固定坐标标记LED和数码管ROI，产品摆放位置的微小偏差（±5-20像素）会导致检测异常，影响系统稳定性和准确性。

### 解决方案
通过基准点模板匹配技术，实现ROI位置的动态自动调整，确保检测精度不受产品摆放位置偏差影响。

### 技术路线
- **基准点选择**: 校准时选择产品上2个固定特征点
- **模板提取**: 自动截取基准点30×30像素模板图像
- **动态匹配**: 检测时实时搜索基准点位置
- **ROI调整**: 根据偏移量自动调整所有ROI坐标

---

## 🎯 功能特性

### ✅ 核心功能
- [x] **全类型ROI支持**: LED ROI + 数码管ROI + 数码管段ROI
- [x] **自动位置校正**: 检测时无需人工干预
- [x] **高精度对齐**: ±2-3像素定位精度  
- [x] **容错机制**: 基准点丢失时回退原始位置
- [x] **配置持久化**: 基准点数据自动保存/加载

### 🚀 性能指标
| 指标 | 目标值 | 备注 |
|------|--------|------|
| 定位精度 | ±2-3像素 | 在±20像素偏差范围内 |
| 成功率 | >95% | 良好照明条件下 |
| 处理延迟 | <10ms | 每帧增加的处理时间 |
| 适用偏差 | ±20像素 | 产品位置偏移范围 |
| 旋转容忍 | <5° | 小幅度旋转适应性 |

---

## 🏗️ 系统架构

### 数据流程图
```
校准阶段:
选择基准点 → 提取模板 → 标记ROI → 保存原始坐标 → 持久化配置

检测阶段:  
读取帧 → 模板匹配 → 计算偏移 → 调整ROI → LED/数码管检测 → 输出结果
```

### 核心组件
1. **基准点管理器** - 负责基准点选择、模板提取和匹配
2. **ROI对齐器** - 计算偏移量并调整所有ROI坐标
3. **配置扩展** - 支持基准点数据的保存和加载
4. **UI交互增强** - 新增基准点校准界面

---

## 📊 详细设计

### 1. 数据结构扩展

#### AppState类新增属性
```python
# 基准点相关
self.base_points = [None, None]  # 两个基准点坐标 [(x1,y1), (x2,y2)]
self.base_templates = [None, None]  # 基准点模板图像 [template1, template2]  
self.template_size = 30  # 模板尺寸 30×30像素
self.base_match_threshold = 0.75  # 模板匹配阈值

# 原始校准坐标（用于计算相对偏移）
self.original_base_points = [None, None]
self.original_led_rois = [None] * max_rois
self.original_digit_rois = [None] * NUM_DIGITS
self.original_digit_segment_rois = [[None] * 7 for _ in range(NUM_DIGITS)]

# 校准状态控制
self.calib_base_point_index = 0  # 当前选择基准点索引(0或1)
self.alignment_enabled = True  # 是否启用动态对齐
self.last_alignment_success = False  # 上次对齐是否成功
self.alignment_fail_count = 0  # 连续对齐失败次数
```

#### Constants常量扩展
```python
# 基准点校准状态
CALIB_STATE_BASE_POINTS_SELECT = 9  # 基准点选择状态

# 基准点匹配参数
BASE_TEMPLATE_SIZE = 30  # 基准点模板大小
BASE_MATCH_THRESHOLD_MIN = 0.7  # 最低匹配阈值
BASE_MATCH_THRESHOLD_DEFAULT = 0.75  # 默认匹配阈值
BASE_ALIGNMENT_TIMEOUT = 5  # 连续失败超时次数
```

### 2. 核心算法实现

#### A. 基准点模板提取
```python
def extract_base_template(frame, center_x, center_y, template_size=30):
    """
    从指定中心点提取模板图像
    """
    half_size = template_size // 2
    x1, y1 = max(0, center_x - half_size), max(0, center_y - half_size)
    x2, y2 = min(frame.shape[1], center_x + half_size), min(frame.shape[0], center_y + half_size)
    
    if x2 <= x1 or y2 <= y1:
        return None
        
    template = frame[y1:y2, x1:x2].copy()
    
    # 验证模板质量（检查对比度）
    if cv2.Laplacian(cv2.cvtColor(template, cv2.COLOR_BGR2GRAY), cv2.CV_64F).var() < 100:
        print("警告：选择的基准点对比度较低，可能影响匹配精度")
    
    return template
```

#### B. 基准点搜索匹配
```python
def find_base_points(frame, app_state):
    """
    在当前帧中搜索基准点位置
    返回: (成功标志, 基准点坐标列表)
    """
    if not app_state.base_templates[0] or not app_state.base_templates[1]:
        return False, []
    
    current_points = []
    match_scores = []
    
    for i in range(2):
        template = app_state.base_templates[i]
        
        # 模板匹配
        result = cv2.matchTemplate(frame, template, cv2.TM_CCOEFF_NORMED)
        _, max_val, _, max_loc = cv2.minMaxLoc(result)
        
        if max_val >= app_state.base_match_threshold:
            # 计算基准点中心坐标
            center_x = max_loc[0] + app_state.template_size // 2
            center_y = max_loc[1] + app_state.template_size // 2
            current_points.append((center_x, center_y))
            match_scores.append(max_val)
        else:
            return False, []
    
    # 验证基准点间距离是否合理（避免误匹配）
    dist = np.sqrt((current_points[0][0] - current_points[1][0])**2 + 
                   (current_points[0][1] - current_points[1][1])**2)
    original_dist = np.sqrt((app_state.original_base_points[0][0] - app_state.original_base_points[1][0])**2 +
                           (app_state.original_base_points[0][1] - app_state.original_base_points[1][1])**2)
    
    if abs(dist - original_dist) > original_dist * 0.2:  # 距离变化超过20%认为异常
        return False, []
    
    return True, current_points
```

#### C. ROI动态对齐
```python
def auto_align_rois(app_state, frame):
    """
    自动对齐所有ROI到当前产品位置
    返回: 对齐成功标志
    """
    if not app_state.alignment_enabled:
        return True  # 禁用时直接返回成功
    
    # 搜索基准点
    success, current_points = find_base_points(frame, app_state)
    
    if not success:
        app_state.alignment_fail_count += 1
        app_state.last_alignment_success = False
        
        if app_state.alignment_fail_count >= BASE_ALIGNMENT_TIMEOUT:
            logging.warning(f"基准点对齐连续失败{app_state.alignment_fail_count}次，建议检查产品位置或光照条件")
        
        return False
    
    # 重置失败计数
    app_state.alignment_fail_count = 0
    app_state.last_alignment_success = True
    
    # 计算平移偏移量
    dx1 = current_points[0][0] - app_state.original_base_points[0][0]
    dy1 = current_points[0][1] - app_state.original_base_points[0][1]
    dx2 = current_points[1][0] - app_state.original_base_points[1][0] 
    dy2 = current_points[1][1] - app_state.original_base_points[1][1]
    
    # 使用平均偏移（简单平移模型）
    dx = int(round((dx1 + dx2) / 2.0))
    dy = int(round((dy1 + dy2) / 2.0))
    
    logging.debug(f"ROI自动对齐: 偏移量=({dx}, {dy}), 匹配分数=[{current_points}]")
    
    # 调整LED ROI
    for i in range(app_state.led_max_rois):
        if app_state.original_led_rois[i]:
            ox, oy, ow, oh = app_state.original_led_rois[i]
            app_state.led_rois[i] = (ox + dx, oy + dy, ow, oh)
    
    # 调整数码管整体ROI
    for i in range(NUM_DIGITS):
        if app_state.original_digit_rois[i]:
            ox, oy, ow, oh = app_state.original_digit_rois[i]
            app_state.digit_rois[i] = (ox + dx, oy + dy, ow, oh)
    
    # 调整数码管段ROI
    for i in range(NUM_DIGITS):
        for j in range(NUM_SEGMENTS_PER_DIGIT):
            if app_state.original_digit_segment_rois[i][j]:
                ox, oy, ow, oh = app_state.original_digit_segment_rois[i][j]
                app_state.digit_segment_rois[i][j] = (ox + dx, oy + dy, ow, oh)
    
    return True
```

### 3. UI交互扩展

#### 校准流程修改
```python
# 在CALIB_STATE_START后插入新状态
if app_state.current_calib_state == CALIB_STATE_START:
    app_state.prompt_message = "校准模式: 'B' 基准点 | 'L' LED | 'D' 数码管 | 'S' 保存退出 | 'Enter' 检测"
    
    if key == ord('b'):  # 新增基准点选择
        app_state.current_calib_state = CALIB_STATE_BASE_POINTS_SELECT
        app_state.calib_base_point_index = 0
        # 清空旧基准点数据
        app_state.base_points = [None, None]
        app_state.base_templates = [None, None]

# 新增基准点选择状态处理
elif app_state.current_calib_state == CALIB_STATE_BASE_POINTS_SELECT:
    idx = app_state.calib_base_point_index
    app_state.status_message = f"基准点校准: 选择特征点 {idx + 1}/2"
    
    if idx < 2:
        app_state.prompt_message = "点击产品上明显特征点(螺丝孔/标记/边角) | 'B' 上一个 | 'Esc' 跳过"
    else:
        app_state.prompt_message = "基准点选择完成！'Enter' 继续LED校准 | 'R' 重选 | 'Esc' 返回"
    
    # 鼠标点击选择基准点
    if event == cv2.EVENT_LBUTTONDOWN and idx < 2:
        # 提取模板
        template = extract_base_template(frame_to_use, x, y, app_state.template_size)
        if template is not None:
            app_state.base_points[idx] = (x, y)
            app_state.base_templates[idx] = template
            print(f"基准点 {idx + 1} 已选择: ({x}, {y}), 模板大小: {template.shape}")
            app_state.calib_base_point_index += 1
        else:
            app_state.prompt_message = "基准点选择失败，请选择图像边界内的位置"
    
    elif key == 13 and idx >= 2:  # Enter继续
        app_state.original_base_points = app_state.base_points.copy()
        app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
        print("基准点校准完成，开始LED ROI选择")
    
    elif key == ord('r'):  # 重选
        app_state.calib_base_point_index = 0
        app_state.base_points = [None, None]
        app_state.base_templates = [None, None]
    
    elif key == 27:  # Esc跳过基准点功能
        app_state.alignment_enabled = False
        app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
        print("跳过基准点校准，ROI将使用固定坐标")
```

#### 检测模式集成
```python
def _run_detection_mode(app_state: AppState):
    # ... 现有代码 ...
    
    # 读取原始帧
    ret, frame = app_state.cap.read()
    if not ret or frame is None:
        # ... 错误处理 ...
        return
    
    original_frame = frame.copy()
    app_state.display_frame = frame.copy()
    
    # ⭐ 新增：ROI自动对齐
    alignment_success = auto_align_rois(app_state, original_frame)
    if not alignment_success and app_state.alignment_enabled:
        status_suffix = f" | 基准点对齐失败({app_state.alignment_fail_count}次)"
        app_state.status_message = (app_state.status_message or "") + status_suffix
    
    # 继续原有检测流程
    if app_state.led_max_rois > 0:
        led_detector.detect_led_status(original_frame, app_state)
    
    # ... 其余代码保持不变 ...
```

### 4. 配置管理扩展

#### config_manager.py修改
```python
def save_config(app_state: AppState) -> bool:
    config = {
        # ... 现有配置项 ...
        
        # 新增基准点配置
        "base_alignment": {
            "enabled": app_state.alignment_enabled,
            "base_points": app_state.base_points,
            "original_base_points": app_state.original_base_points,
            "template_size": app_state.template_size,
            "match_threshold": app_state.base_match_threshold,
            # 基准点模板图像(base64编码)
            "base_templates_b64": [
                base64.b64encode(cv2.imencode('.png', tmpl)[1]).decode('utf-8') 
                if tmpl is not None else None
                for tmpl in app_state.base_templates
            ]
        },
        
        # 原始ROI坐标（用于偏移计算）
        "original_rois": {
            "led_rois": app_state.original_led_rois,
            "digit_rois": app_state.original_digit_rois,
            "digit_segment_rois": app_state.original_digit_segment_rois
        }
    }
    
    # ... 保存逻辑 ...

def load_config(app_state: AppState) -> bool:
    # ... 加载逻辑 ...
    
    # 加载基准点配置
    if "base_alignment" in config:
        base_config = config["base_alignment"]
        app_state.alignment_enabled = base_config.get("enabled", True)
        app_state.base_points = base_config.get("base_points", [None, None])
        app_state.original_base_points = base_config.get("original_base_points", [None, None])
        app_state.template_size = base_config.get("template_size", BASE_TEMPLATE_SIZE)
        app_state.base_match_threshold = base_config.get("match_threshold", BASE_MATCH_THRESHOLD_DEFAULT)
        
        # 解码基准点模板
        templates_b64 = base_config.get("base_templates_b64", [None, None])
        app_state.base_templates = []
        for tmpl_b64 in templates_b64:
            if tmpl_b64:
                img_data = base64.b64decode(tmpl_b64)
                template = cv2.imdecode(np.frombuffer(img_data, np.uint8), cv2.IMREAD_COLOR)
                app_state.base_templates.append(template)
            else:
                app_state.base_templates.append(None)
    
    # 加载原始ROI坐标
    if "original_rois" in config:
        orig_rois = config["original_rois"]
        app_state.original_led_rois = orig_rois.get("led_rois", [None] * app_state.led_max_rois)
        app_state.original_digit_rois = orig_rois.get("digit_rois", [None] * NUM_DIGITS)
        app_state.original_digit_segment_rois = orig_rois.get("digit_segment_rois", 
            [[None] * NUM_SEGMENTS_PER_DIGIT for _ in range(NUM_DIGITS)])
```

### 5. ROI坐标保存机制

在各校准阶段完成后，自动保存原始坐标：

```python
# LED ROI确认时
if key == 13 and app_state.current_rect:  # Enter确认
    app_state.led_rois[idx] = app_state.current_rect
    # ⭐ 同时保存到原始坐标
    app_state.original_led_rois[idx] = app_state.current_rect
    
# 数码管ROI确认时  
if key == 13 and app_state.current_rect:
    app_state.digit_rois[digit_idx] = app_state.current_rect
    # ⭐ 同时保存到原始坐标
    app_state.original_digit_rois[digit_idx] = app_state.current_rect
    
# 数码管段ROI确认时
if key == 13 and app_state.current_rect:
    app_state.digit_segment_rois[d_idx][s_idx] = app_state.current_rect  
    # ⭐ 同时保存到原始坐标
    app_state.original_digit_segment_rois[d_idx][s_idx] = app_state.current_rect
```

---

## 📝 实施计划

### 第一阶段：基础架构 (2-3小时)
- [ ] 扩展AppState类，添加基准点相关属性
- [ ] 更新constants.py，添加新常量定义
- [ ] 创建基准点管理核心函数框架

### 第二阶段：校准流程 (3-4小时)  
- [ ] 实现基准点选择UI交互逻辑
- [ ] 开发基准点模板提取算法
- [ ] 集成基准点校准到现有校准流程

### 第三阶段：对齐算法 (2-3小时)
- [ ] 实现模板匹配搜索算法
- [ ] 开发ROI自动对齐核心逻辑
- [ ] 集成到检测模式主流程

### 第四阶段：配置管理 (2小时)
- [ ] 扩展配置文件保存/加载功能
- [ ] 实现基准点模板图像序列化
- [ ] 添加原始ROI坐标持久化

### 第五阶段：测试优化 (2-3小时)
- [ ] 功能集成测试
- [ ] 参数调优（匹配阈值、模板大小等）
- [ ] 边界情况处理和容错机制完善

**总预计工时: 11-15小时**

---

## 🧪 测试验证

### 功能测试用例

#### 1. 基准点选择测试
- [ ] 选择高对比度特征点（螺丝孔、印刷标记）
- [ ] 选择低对比度区域（验证警告机制）
- [ ] 选择图像边界位置（验证边界保护）
- [ ] 重复选择同一位置（验证覆盖逻辑）

#### 2. 模板匹配测试  
- [ ] 产品无偏移（匹配分数>0.9）
- [ ] 小幅平移±5像素（匹配成功）
- [ ] 中度平移±15像素（匹配成功）
- [ ] 大幅平移±25像素（匹配失败，回退机制生效）
- [ ] 轻微旋转<3°（匹配成功）
- [ ] 明显旋转>10°（匹配失败）

#### 3. ROI对齐精度测试
- [ ] LED ROI对齐误差<3像素
- [ ] 数码管整体ROI对齐误差<3像素
- [ ] 数码管段ROI对齐误差<2像素
- [ ] 多次连续对齐结果稳定性

#### 4. 异常情况测试
- [ ] 基准点被遮挡（部分遮挡容忍度）
- [ ] 光照条件变化（亮度±20%影响）
- [ ] 产品表面反光干扰
- [ ] 连续对齐失败的降级处理

### 性能基准测试

| 测试项目 | 目标指标 | 测试方法 |
|---------|----------|-----------|
| 模板匹配速度 | <5ms/模板 | 1000次匹配计时 |
| 整体对齐延迟 | <10ms | 集成到检测流程测量 |
| 内存占用增量 | <50MB | 对比启用前后内存使用 |
| CPU使用率增量 | <5% | 连续运行1小时监控 |

---

## ⚠️ 风险与应对

### 主要风险点

#### 1. 基准点选择不当
**风险**: 用户选择变化的区域或低对比度区域作为基准点
**应对**: 
- 模板质量检测（对比度阈值验证）
- UI提示引导用户选择合适特征点  
- 提供基准点质量评分反馈

#### 2. 光照环境变化
**风险**: 生产环境光照变化影响模板匹配精度
**应对**:
- 自适应匹配阈值调整
- 多尺度模板匹配增强鲁棒性
- 光照补偿预处理

#### 3. 产品变形或旋转
**风险**: 产品非刚性变形或明显旋转导致对齐失败
**应对**:
- 基准点距离变化检测
- 旋转角度估算和补偿（后期版本）
- 优雅降级到原始ROI坐标

#### 4. 性能影响
**风险**: 每帧模板匹配增加计算负担
**应对**:
- 算法优化（ROI区域搜索、多线程）
- 降采样加速（必要时）
- 可选启用/禁用功能

### 兜底策略
1. **匹配失败**: 自动回退到原始固定ROI坐标
2. **连续失败**: 超过阈值后暂时禁用对齐，发出警告
3. **配置损坏**: 检测到异常时重置为默认配置
4. **性能降级**: 检测到帧率下降时自动调整参数

---

## 🔧 调试与维护

### 调试信息输出
```python
# 调试模式开关
DEBUG_BASE_ALIGNMENT = True

# 关键信息日志
logging.debug(f"基准点匹配: P1=({x1},{y1},score={score1:.3f}), P2=({x2},{y2},score={score2:.3f})")
logging.debug(f"ROI偏移: dx={dx}, dy={dy}")
logging.info(f"对齐状态: {'成功' if success else '失败'}, 连续失败次数: {fail_count}")

# 可视化调试（开发阶段）
if DEBUG_BASE_ALIGNMENT:
    # 在显示帧上绘制基准点搜索区域
    cv2.rectangle(display_frame, search_area, (255, 255, 0), 2)
    cv2.circle(display_frame, current_base_point, 5, (0, 255, 255), -1)
```

### 运维监控指标
- 基准点匹配成功率（按小时统计）
- 平均匹配分数趋势
- ROI对齐失败频率
- 系统性能影响度量

### 配置调优参数
```python
# 可通过配置文件调整的参数
{
  "base_alignment": {
    "template_size": 30,           # 模板大小
    "match_threshold": 0.75,       # 匹配阈值
    "distance_tolerance": 0.2,     # 基准点距离变化容忍度  
    "fail_timeout": 5,             # 连续失败超时次数
    "enable_rotation_compensation": false  # 旋转补偿（预留）
  }
}
```

---

## 🚀 后续扩展计划

### v1.1 增强功能
- [ ] **旋转补偿**: 支持产品小幅旋转的自动校正
- [ ] **多模板匹配**: 每个基准点使用多个角度/光照模板
- [ ] **智能阈值**: 根据环境自适应调整匹配阈值

### v1.2 性能优化
- [ ] **ROI区域搜索**: 限制搜索范围提升性能
- [ ] **多线程匹配**: 并行处理两个基准点匹配
- [ ] **GPU加速**: 利用OpenCV GPU模块加速模板匹配

### v1.3 易用性提升
- [ ] **基准点质量评估**: 实时评分指导用户选择
- [ ] **一键重校准**: 检测到产品更换时快速重新校准
- [ ] **校准向导**: 图形化引导完整校准流程

---

## 📞 技术支持

### 常见问题解决

**Q: 基准点匹配失败率高怎么办？**
A: 
1. 检查基准点选择是否合适（高对比度、稳定特征）
2. 调整匹配阈值（降低到0.65-0.7）
3. 改善照明条件，减少阴影和反光
4. 重新选择更明显的基准点

**Q: ROI对齐精度不够怎么办？**  
A:
1. 增加基准点模板大小（35-40像素）
2. 选择距离ROI更近的基准点
3. 检查基准点是否存在微小移动
4. 考虑使用亚像素匹配算法

**Q: 系统性能下降明显怎么办？**
A:
1. 减小模板搜索区域
2. 降低模板分辨率
3. 调整匹配算法（使用TM_SQDIFF_NORMED）
4. 临时禁用对齐功能

### 联系方式
- 技术负责人: [开发者姓名]
- 项目仓库: [Git仓库地址] 
- 问题反馈: [Issue跟踪链接]

---

**文档版本**: v1.0  
**创建日期**: 2025-08-07  
**最后更新**: 2025-08-07  
**状态**: 待实施