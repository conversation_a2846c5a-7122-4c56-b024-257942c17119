import asyncio
import websockets
import json
import threading
import time
from datetime import datetime
import logging

class VisualWebSocketServer:
    def __init__(self, port=8765):
        self.port = port
        self.clients = set()
        self.server = None
        self.running = False
        self.current_state = {
            'state': 'IDLE',
            'countdown': 0,
            'total_duration': 0,
            'progress': 0,
            'start_time': '',
            'message': '等待检测...',
            'is_active': False
        }
        
    async def register_client(self, websocket, path):
        """注册新客户端"""
        self.clients.add(websocket)
        print(f"✓ 浏览器客户端已连接，当前连接数: {len(self.clients)}")
        
        # 发送连接确认消息
        await self.send_to_client(websocket, {
            "type": "connection_confirmed",
            "data": {
                "message": "本地视觉检测程序已连接",
                "version": "1.0.0",
                "current_state": self.current_state
            }
        })
        
        try:
            # 监听客户端消息
            async for message in websocket:
                await self.handle_client_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            pass
        finally:
            self.clients.remove(websocket)
            print(f"✗ 客户端断开连接，当前连接数: {len(self.clients)}")
    
    async def handle_client_message(self, websocket, message):
        """处理客户端消息"""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'query_status':
                # 响应状态查询
                await self.send_to_client(websocket, {
                    "type": "status_response",
                    "data": self.current_state.copy()
                })
                print(f"📤 响应状态查询: {self.current_state['state']}")
                
        except json.JSONDecodeError:
            print(f"❌ 收到无效JSON消息: {message}")
        except Exception as e:
            print(f"❌ 处理客户端消息失败: {e}")
    
    async def send_to_client(self, websocket, message):
        """发送消息给指定客户端"""
        try:
            await websocket.send(json.dumps(message))
        except websockets.exceptions.ConnectionClosed:
            if websocket in self.clients:
                self.clients.remove(websocket)
        except Exception as e:
            print(f"发送消息失败: {e}")
    
    async def broadcast_message(self, message):
        """广播消息给所有连接的客户端"""
        if self.clients:
            disconnected = set()
            for client in self.clients.copy():
                try:
                    await client.send(json.dumps(message))
                except websockets.exceptions.ConnectionClosed:
                    disconnected.add(client)
                except Exception as e:
                    print(f"发送消息失败: {e}")
                    disconnected.add(client)
            
            # 清理断开的连接
            self.clients -= disconnected
    
    def broadcast_sync(self, message):
        """同步方式广播消息（从主线程调用）"""
        if self.running and self.clients and hasattr(self, 'loop') and self.loop:
            # 更新当前状态
            if message.get('type') in ['countdown_update', 'status_response']:
                self.current_state.update(message.get('data', {}))

            try:
                # 在事件循环中执行异步广播
                asyncio.run_coroutine_threadsafe(
                    self.broadcast_message(message),
                    self.loop
                )
            except Exception as e:
                print(f"广播消息失败: {e}")
    
    def update_state(self, state, countdown=0, total_duration=0, message="", is_active=False):
        """更新当前状态"""
        progress = 0
        if total_duration > 0:
            progress = int((total_duration - countdown) / total_duration * 100)
        
        self.current_state = {
            'state': state,
            'countdown': int(countdown),
            'total_duration': int(total_duration),
            'progress': progress,
            'start_time': datetime.now().strftime("%H:%M:%S"),
            'message': message,
            'is_active': is_active
        }
        
        # 广播状态更新
        self.broadcast_sync({
            "type": "countdown_update",
            "data": self.current_state.copy()
        })
    
    def start_server(self):
        """启动WebSocket服务器"""
        def run_server():
            # 创建新的事件循环
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)

            try:
                # 启动WebSocket服务器
                start_server_coro = websockets.serve(
                    self.register_client,
                    "127.0.0.1",
                    self.port
                )

                self.running = True
                print(f"✓ WebSocket服务器已启动: ws://127.0.0.1:{self.port}")

                # 运行服务器
                self.loop.run_until_complete(start_server_coro)
                self.loop.run_forever()

            except Exception as e:
                print(f"✗ WebSocket服务器启动失败: {e}")
                self.running = False
            finally:
                if hasattr(self, 'loop') and self.loop:
                    self.loop.close()

        # 在单独线程中运行服务器
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()

        # 等待服务器启动
        time.sleep(2)  # 增加等待时间
        return server_thread
    
    def stop_server(self):
        """停止WebSocket服务器"""
        self.running = False
        if hasattr(self, 'loop'):
            self.loop.call_soon_threadsafe(self.loop.stop)

# 全局WebSocket服务器实例
websocket_server = VisualWebSocketServer()
