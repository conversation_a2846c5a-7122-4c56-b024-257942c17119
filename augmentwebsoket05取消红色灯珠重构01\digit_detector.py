import cv2
import numpy as np
from app_state import AppState
from constants import * # 导入 SEGMENT_MAP, CHAR_TO_SEGMENTS 等

def calculate_bright_pixel_brightness(segment_area, bright_percent=BRIGHT_PIXEL_PERCENT):
    """
    计算数码管段最亮N%像素的平均亮度

    Args:
        segment_area: 数码管段区域图像 (H, W) - 灰度图
        bright_percent: 采样最亮像素的百分比 (0.0-1.0)

    Returns:
        mean_brightness: 最亮像素的平均亮度值
    """
    if not ENABLE_PRECISION_OPT_STAGE1 or bright_percent <= 0:
        # 功能关闭或参数无效时，回退到原始方法
        return cv2.mean(segment_area)[0]

    if segment_area.size == 0:
        return 0.0

    # 展平为一维数组
    gray_vals = segment_area.flatten()

    # ROI太小时回退到原始方法
    if len(gray_vals) < 10:
        return cv2.mean(segment_area)[0]

    # 计算亮度阈值（最亮N%的下界）
    thresh = np.percentile(gray_vals, 100 * (1 - bright_percent))

    # 创建掩码：选择最亮的N%像素
    mask = gray_vals >= thresh

    # 防止极端情况：最亮像素太少
    if mask.sum() < 3:
        return cv2.mean(segment_area)[0]

    # 计算最亮像素的平均亮度
    return gray_vals[mask].mean()

def detect_digit_status(frame: np.ndarray, app_state: AppState):
    """识别数码管字符并返回状态"""
    if frame is None:
        # 返回与 AppState 结构匹配的默认值
        app_state.digit_last_recognized_chars = [None] * NUM_DIGITS
        app_state.digit_last_segment_patterns = [[0] * NUM_SEGMENTS_PER_DIGIT for _ in range(NUM_DIGITS)]
        app_state.digit_last_missing_segments = [[] for _ in range(NUM_DIGITS)]
        return app_state.digit_last_recognized_chars, app_state.digit_last_segment_patterns, app_state.digit_last_missing_segments

    # 初始化结果列表
    recognized_chars = [None] * NUM_DIGITS
    segment_patterns = [[0] * NUM_SEGMENTS_PER_DIGIT for _ in range(NUM_DIGITS)]
    missing_segments = [[] for _ in range(NUM_DIGITS)]

    try:
        gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    except cv2.error as e:
        print(f"错误：无法将帧转换为灰度图进行数字检测: {e}")
        # 返回之前的状态或默认值
        return app_state.digit_last_recognized_chars, app_state.digit_last_segment_patterns, app_state.digit_last_missing_segments


    frame_h, frame_w = gray_frame.shape[:2]

    for d_idx in range(NUM_DIGITS):
        # 检查数字 ROI 是否已定义
        digit_roi = app_state.digit_rois[d_idx]
        if not digit_roi or not isinstance(digit_roi, (list, tuple)) or len(digit_roi) != 4:
            continue # 跳过未设置的数码管

        current_digit_pattern = [0] * NUM_SEGMENTS_PER_DIGIT
        all_segments_defined = True # 标记该数字的段是否都定义了

        for s_idx in range(NUM_SEGMENTS_PER_DIGIT):
            segment_roi = app_state.digit_segment_rois[d_idx][s_idx]
            # 检查段 ROI 是否已定义和有效
            if not segment_roi or not isinstance(segment_roi, (list, tuple)) or len(segment_roi) != 4:
                all_segments_defined = False
                continue # 跳过未设置的段

            sx, sy, sw, sh = segment_roi
            if sw <= 0 or sh <= 0:
                 all_segments_defined = False
                 continue # 跳过无效尺寸

            # 边界检查和裁剪
            y_end = min(sy + sh, frame_h)
            x_end = min(sx + sw, frame_w)
            roi_y = max(0, sy)
            roi_x = max(0, sx)

            if roi_y >= y_end or roi_x >= x_end:
                 all_segments_defined = False
                 continue # ROI 在帧外

            segment_area = gray_frame[roi_y:y_end, roi_x:x_end]
            if segment_area.size == 0:
                 all_segments_defined = False
                 continue

            try:
                # 使用最亮像素采样算法计算亮度
                mean_brightness = calculate_bright_pixel_brightness(segment_area)
                # 使用 AppState 中的阈值判断是否亮起
                is_on = mean_brightness > app_state.digit_brightness_threshold
                current_digit_pattern[s_idx] = 1 if is_on else 0
            except Exception as e:
                 print(f"计算数字 {d_idx+1} 段 {s_idx} 亮度时出错: {e}")
                 all_segments_defined = False # 出错也认为段未完全定义

        # 保存检测到的模式
        segment_patterns[d_idx] = current_digit_pattern

        # 只有当所有段都有效定义时才进行识别
        if all_segments_defined:
            current_pattern_tuple = tuple(current_digit_pattern)
            # 从常量中查找识别字符
            recognized_char = SEGMENT_MAP.get(current_pattern_tuple)
            recognized_chars[d_idx] = recognized_char if recognized_char is not None else '?' # 未知模式显示 '?'

            # 检查缺陷 (仅当成功识别且不是全灭'-')
            if recognized_char and recognized_char != '-':
                # 从常量中获取预期模式
                expected_pattern = CHAR_TO_SEGMENTS.get(recognized_char)
                if expected_pattern: # 确保字符在反向映射中
                    missing = []
                    for i in range(NUM_SEGMENTS_PER_DIGIT):
                        # 期望亮 (1)，实际灭 (0)
                        if expected_pattern[i] == 1 and current_pattern_tuple[i] == 0:
                            missing.append(i)
                        # 可选：检查期望灭 (0)，实际亮 (1) -> 可能指示阈值问题或噪声
                        # elif expected_pattern[i] == 0 and current_pattern_tuple[i] == 1:
                        #     print(f"Debug: Digit {d_idx+1}, Segment {i} unexpected ON for char {recognized_char}")
                    missing_segments[d_idx] = missing
                # else: # 识别出的字符不在反向映射中? 这不应该发生，除非常量定义有问题
                #     print(f"警告：识别字符 '{recognized_char}' 不在 CHAR_TO_SEGMENTS 中")
                #     recognized_chars[d_idx] = '?' # 标记为未知
                #     pass # Fix indentation here

        else: # 如果段未完全定义，则无法识别
             recognized_chars[d_idx] = None # 或 '?'

    # 更新 AppState
    app_state.digit_last_recognized_chars = recognized_chars
    app_state.digit_last_segment_patterns = segment_patterns
    app_state.digit_last_missing_segments = missing_segments

    return recognized_chars, segment_patterns, missing_segments
