#!/usr/bin/env python3
"""
测试异步任务管理器功能的脚本
验证异步处理是否正常工作，不影响现有功能
"""

import time
import logging
import os
from async_task_manager import AsyncTaskManager, TaskType, TaskStatus

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_async_task_manager():
    """测试异步任务管理器的基本功能"""
    print("=" * 50)
    print("测试异步任务管理器")
    print("=" * 50)
    
    # 创建并启动任务管理器
    manager = AsyncTaskManager()
    manager.start()
    
    try:
        # 测试1: CPU通信任务（模拟）
        print("\n1. 测试CPU通信任务...")
        cpu_task_id = manager.submit_task(
            TaskType.SEND_CPU_SIGNAL,
            {'value': 1, 'address': 10}
        )
        print(f"提交CPU任务: {cpu_task_id}")
        
        # 等待任务完成
        start_time = time.time()
        while time.time() - start_time < 10:  # 最多等待10秒
            status = manager.get_task_status(cpu_task_id)
            print(f"任务状态: {status}")
            
            if status == TaskStatus.COMPLETED:
                result = manager.get_task_result(cpu_task_id)
                print(f"任务完成，结果: {result}")
                break
            elif status == TaskStatus.FAILED:
                error = manager.get_task_error(cpu_task_id)
                print(f"任务失败，错误: {error}")
                break
                
            time.sleep(1)
        
        # 测试2: 日志分析任务（如果日志文件存在）
        log_file = "led_digit_detection.log"
        if os.path.exists(log_file):
            print(f"\n2. 测试日志分析任务...")
            analysis_task_id = manager.submit_task(
                TaskType.ANALYZE_LOG,
                {'log_file_path': log_file}
            )
            print(f"提交分析任务: {analysis_task_id}")
            
            # 等待任务完成
            start_time = time.time()
            while time.time() - start_time < 30:  # 最多等待30秒
                status = manager.get_task_status(analysis_task_id)
                print(f"分析任务状态: {status}")
                
                if status == TaskStatus.COMPLETED:
                    result = manager.get_task_result(analysis_task_id)
                    print(f"分析完成，结果类型: {type(result)}")
                    if result:
                        print(f"完美周期数: {result.get('perfect_cycles', 'N/A')}")
                        print(f"特殊LED状态: {result.get('special_leds_status', 'N/A')}")
                    break
                elif status == TaskStatus.FAILED:
                    error = manager.get_task_error(analysis_task_id)
                    print(f"分析失败，错误: {error}")
                    break
                    
                time.sleep(2)
        else:
            print(f"\n2. 跳过日志分析测试（日志文件 {log_file} 不存在）")
        
        # 测试3: 清理功能
        print(f"\n3. 测试任务清理...")
        manager.cleanup_old_tasks(max_age_seconds=1)  # 清理1秒前的任务
        print("任务清理完成")
        
        print(f"\n✓ 异步任务管理器测试完成")
        
    finally:
        # 停止任务管理器
        manager.stop()
        print("任务管理器已停止")

def test_ui_integration():
    """测试UI集成（模拟）"""
    print("\n" + "=" * 50)
    print("测试UI集成（模拟）")
    print("=" * 50)
    
    # 模拟AppState
    class MockAppState:
        def __init__(self):
            self.led_analysis_state = 'IDLE'
            self.current_analysis_task_id = None
            self.current_cpu_task_id = None
            self.async_task_progress = ""
            self.last_analysis_result = None
            self.last_special_leds_result = None
            self.last_task_cleanup_time = 0.0
    
    app_state = MockAppState()
    manager = AsyncTaskManager()
    manager.start()
    
    try:
        print("模拟88信号触发的分析流程...")
        
        # 模拟状态转换: IDLE -> ANALYZING
        app_state.led_analysis_state = 'ANALYZING'
        print(f"状态: {app_state.led_analysis_state}")
        
        # 提交分析任务
        if app_state.current_analysis_task_id is None:
            # 创建一个测试日志文件
            test_log = "test_led_log.txt"
            with open(test_log, 'w', encoding='utf-8') as f:
                f.write("2024-01-01 12:00:00.000 - INFO - LED G1: Status=ON\n")
                f.write("2024-01-01 12:00:01.000 - INFO - LED G1: Status=OFF\n")
            
            task_id = manager.submit_task(
                TaskType.ANALYZE_LOG,
                {'log_file_path': test_log}
            )
            app_state.current_analysis_task_id = task_id
            app_state.async_task_progress = "正在分析日志文件..."
            print(f"提交分析任务: {task_id}")
        
        # 模拟主循环检查任务状态
        for i in range(10):
            status = manager.get_task_status(app_state.current_analysis_task_id)
            print(f"循环 {i+1}: 任务状态 = {status}, 进度 = {app_state.async_task_progress}")
            
            if status == TaskStatus.COMPLETED:
                result = manager.get_task_result(app_state.current_analysis_task_id)
                app_state.last_analysis_result = result.get('perfect_cycles', 0) if result else 0
                app_state.led_analysis_state = 'SENDING_RESULT_10'
                app_state.current_analysis_task_id = None
                app_state.async_task_progress = ""
                print(f"分析完成，转换到状态: {app_state.led_analysis_state}")
                break
            elif status == TaskStatus.FAILED:
                error = manager.get_task_error(app_state.current_analysis_task_id)
                print(f"分析失败: {error}")
                app_state.led_analysis_state = 'CLEARING'
                break
            
            time.sleep(0.5)  # 模拟UI更新间隔
        
        # 清理测试文件
        if os.path.exists(test_log):
            os.remove(test_log)
        
        print("✓ UI集成测试完成")
        
    finally:
        manager.stop()

def main():
    """主测试函数"""
    print("开始异步功能测试...")
    
    try:
        test_async_task_manager()
        test_ui_integration()
        print("\n" + "=" * 50)
        print("✓ 所有测试通过！异步处理功能正常工作")
        print("✓ 现有功能不受影响")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
