#!/usr/bin/env python3
"""
测试固定尺寸模板功能的简单脚本
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app_state import AppState
from constants import *
import config_manager

def test_fixed_template_functionality():
    """测试固定尺寸模板功能"""
    print("=== 测试固定尺寸模板功能 ===")
    
    # 创建 AppState 实例
    app_state = AppState()
    
    # 测试默认值
    print(f"默认固定模板大小: {app_state.fixed_template_size}")
    print(f"默认固定模板模式: {app_state.fixed_template_mode}")
    
    # 测试模式切换
    app_state.fixed_template_mode = True
    print(f"切换到固定模板模式: {app_state.fixed_template_mode}")
    
    # 测试配置保存和加载
    print("\n=== 测试配置保存和加载 ===")
    
    # 修改固定模板大小
    app_state.fixed_template_size = 15
    print(f"修改固定模板大小为: {app_state.fixed_template_size}")
    
    # 保存配置
    try:
        config_manager.save_config(app_state)
        print("✓ 配置保存成功")
    except Exception as e:
        print(f"✗ 配置保存失败: {e}")
        return False
    
    # 创建新的 AppState 实例并加载配置
    app_state_new = AppState()
    print(f"新实例默认固定模板大小: {app_state_new.fixed_template_size}")
    
    try:
        config_manager.load_config(app_state_new)
        print(f"加载后固定模板大小: {app_state_new.fixed_template_size}")
        
        if app_state_new.fixed_template_size == 15:
            print("✓ 配置加载成功，固定模板大小正确")
        else:
            print("✗ 配置加载失败，固定模板大小不正确")
            return False
            
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        return False
    
    print("\n=== 测试完成 ===")
    return True

def test_constants():
    """测试常量定义"""
    print("=== 测试常量定义 ===")
    
    try:
        print(f"DEFAULT_FIXED_TEMPLATE_SIZE: {DEFAULT_FIXED_TEMPLATE_SIZE}")
        print("✓ 常量定义正确")
        return True
    except NameError as e:
        print(f"✗ 常量定义错误: {e}")
        return False

if __name__ == "__main__":
    print("开始测试固定尺寸模板功能...\n")
    
    # 测试常量
    if not test_constants():
        print("常量测试失败，退出")
        sys.exit(1)
    
    # 测试功能
    if test_fixed_template_functionality():
        print("\n所有测试通过！")
        sys.exit(0)
    else:
        print("\n测试失败！")
        sys.exit(1)
