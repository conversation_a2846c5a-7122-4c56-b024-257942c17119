2025/4/18:
1、修改led_detector.py的逻辑，取消红色灯珠
对所有 LED 计算基于其独立阈值的初始状态。
仅对绿色 LED 应用初步相对亮度抑制。
更新历史记录时，为绿色 LED 存储抑制后的状态，为红色 LED 存储原始状态。
仅对绿色 LED 应用时间平滑。红色 LED 的状态将基于其最新的原始状态。
仅对绿色 LED 应用增强的互斥逻辑抑制。
在记录状态变化和更新最终用于 UI 显示的 app_state.led_last_status 时，对绿色 LED 使用经过完整处理（滤波和抑制）的状态，对红色 LED 使用原始的、仅基于阈值判断的状态。

20250805:增加roi 按b 回退取消当前标记的
BEST03_v09.md:添加模板复制、可连续复制回退。按T复制、B回去并取消复制
BEST03_v10.md:添加模板复制、可连续复制回退。按T复制、B回去并取消复制、E编辑移动
BEST03_v11.md:添加模板复制、可连续复制回退。按T复制、B回去并取消复制、E编辑移动.修改了所有选择边框线改为1像素更精确

BEST03_v12.md:添加模板复制、可连续复制回退。按T复制、B回去并取消复制、E编辑移动.修改了所有选择边框线改为1像素更精确，修改了cpu_communicator.py的53 -63 、注释原来的URL请求添加新的错误处理。 20250826测试用

BEST03_v12.md:添加2个基准点动态配置roi，解决位置偏差问题
BEST03_v15.md:添加2个基准点动态配置roi，解决位置偏差问题，可用状态

BEST03_v16.md:修改提示字体
BEST03_v18.md:增加自定义日志时间/配置启动的时候自定义保存日志记录的取值时间保存到配置文件里面、若为保存采用配置的默认值 last
BEST03_v19:更新LED样本数据和阈值，而保留ROI坐标等其他配置不变。这样可以避免重新校准ROI坐标的繁琐过程。
BEST03_v23：------
                    在任一支持的 ROI 选择界面（LED ROI、数码管 ROI、段 ROI 等），先拖拽出一个矩形（出现蓝框）
                    然后即可：
                    W/A/S/D：上/左/下/右移动当前位置
                    Shift+W/S/A/D：缩小/增大高度，缩小/增大宽度
                    数字键 1-9：调整步长为 1~9 像素（默认 1）
                    界面右上角会显示当前步长；控制台实时打印“微调: 左移 1px”等反馈

BEST03_v24:最终使用版本01

BEST03_v26:采用异步处理优化网络和UI页面解决网络不同界面卡住的问题；添加快捷键A 生成指定大小的模板

BEST03_v27:添加G33数码管绿灯单独处理、3 / #：调整G33灰度阈值（减少/增加）、e / E：调整G33绿色通道阈值（减少/增加）
BEST03_v28:和27一样、只是优化了G33在led_detector.py里计算的逻辑更简洁