#!/usr/bin/env python3
"""
服务状态检查脚本
检查所有必要服务是否正常运行
"""

import requests
import websockets
import asyncio
import socket
import time

def check_port(host, port, timeout=3):
    """检查端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def check_proxy_service():
    """检查本地代理服务"""
    print("🔍 检查本地代理服务 (端口5000)...")
    
    if not check_port('127.0.0.1', 5000):
        print("❌ 端口5000未开放，代理服务未启动")
        return False
    
    try:
        response = requests.get('http://127.0.0.1:5000/', timeout=5)
        if response.status_code == 200:
            print("✅ 本地代理服务正常运行")
            return True
        else:
            print(f"⚠️ 代理服务响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 代理服务连接失败: {e}")
        return False

async def check_websocket_service():
    """检查WebSocket服务"""
    print("🔍 检查视觉检测WebSocket服务 (端口8765)...")
    
    if not check_port('127.0.0.1', 8765):
        print("❌ 端口8765未开放，WebSocket服务未启动")
        return False
    
    try:
        async with websockets.connect('ws://127.0.0.1:8765', timeout=5) as websocket:
            # 发送测试消息
            await websocket.send('{"type": "query_status", "data": {}}')
            response = await asyncio.wait_for(websocket.recv(), timeout=3)
            print("✅ WebSocket服务正常运行")
            return True
    except Exception as e:
        print(f"❌ WebSocket服务连接失败: {e}")
        return False

def check_web_service():
    """检查Web服务"""
    print("🔍 检查Flask Web服务...")
    
    # 检查常见端口
    ports = [3308, 5000, 8080]
    for port in ports:
        if check_port('192.168.1.57', port) or check_port('127.0.0.1', port):
            try:
                url = f'http://127.0.0.1:{port}' if port != 3308 else f'http://192.168.1.57:{port}'
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    print(f"✅ Web服务运行在端口{port}")
                    return True
            except:
                continue
    
    print("❌ Web服务未找到")
    return False

def test_device_connection():
    """测试设备连接"""
    print("🔍 测试设备连接...")
    
    try:
        response = requests.get(
            'http://127.0.0.1:5000/proxy/device-info?ip=*************',
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ 设备连接测试成功")
                return True
            else:
                print(f"⚠️ 设备连接失败: {data.get('message')}")
                return False
        else:
            print(f"❌ 代理请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 设备连接测试失败: {e}")
        return False

async def main():
    """主检查函数"""
    print("=" * 50)
    print("    系统服务状态检查")
    print("=" * 50)
    print()
    
    results = []
    
    # 检查各项服务
    results.append(("本地代理服务", check_proxy_service()))
    results.append(("WebSocket服务", await check_websocket_service()))
    results.append(("Web服务", check_web_service()))
    results.append(("设备连接", test_device_connection()))
    
    print()
    print("=" * 50)
    print("    检查结果汇总")
    print("=" * 50)
    
    all_ok = True
    for service, status in results:
        status_text = "✅ 正常" if status else "❌ 异常"
        print(f"{service:<15} {status_text}")
        if not status:
            all_ok = False
    
    print()
    if all_ok:
        print("🎉 所有服务运行正常！")
        print("💡 您可以开始使用系统了")
    else:
        print("⚠️ 部分服务异常，请检查：")
        print("1. 运行 '启动所有服务.bat' 启动所有服务")
        print("2. 检查防火墙设置")
        print("3. 确认网络连接正常")
    
    print()
    print("按回车键退出...")
    input()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n检查已取消")
    except Exception as e:
        print(f"\n检查过程出错: {e}")
        input("按回车键退出...")
