# G33、R1、R2特殊LED时间计算逻辑说明

## 概述

本文档详细说明了35点位LED检测系统中三个特殊LED（G33、R1、R2）的ON状态时间计算逻辑和好坏判断规则。

## 基本判断标准

**所有特殊LED的统一标准**：
- **好（GOOD）**：总ON时间 > 1.0秒
- **坏（BAD）**：总ON时间 ≤ 1.0秒

## 时间计算逻辑

### 1. G33（绿色LED，ROI 33）

#### 特征
- 位置：ROI 33
- 类型：绿色LED
- 行为模式：有状态变化事件

#### 计算逻辑
1. **查找状态变化事件**：搜索日志中的 `LED G33 state OFF->ON` 和 `LED G33 state ON->OFF` 事件
2. **计算ON时间段**：
   - 每次 `OFF->ON` 开始计时
   - 每次 `ON->OFF` 结束计时，累加时间段
   - 如果最后还在ON状态（没有对应的OFF事件），计算到日志结束的时间
3. **总时间**：所有ON时间段的累加

#### 实际案例
```
状态变化事件：
1. 16:04:59.511: OFF -> ON
2. 16:05:00.007: ON -> OFF  (持续 0.496秒)
3. 16:05:00.615: OFF -> ON  (持续到日志结束 38.511秒)

总ON时间：0.496 + 38.511 = 39.007秒 → GOOD
```

### 2. R1（红色LED 1）

#### 特征
- 位置：对应ROI 34（内部编号为1）
- 类型：红色LED
- 行为模式：持续ON状态，无状态变化

#### 计算逻辑
1. **查找状态变化事件**：搜索日志中的 `LED R1 state` 变化事件
2. **如果无状态变化事件**：
   - 搜索所有 `LED R1: Status=ON` 记录
   - 检查是否所有记录都是ON状态
   - 如果是，计算从第一条记录到最后一条记录的时间差
3. **如果有状态变化事件**：按G33的逻辑处理

#### 实际案例
```
无状态变化事件，但所有状态记录都显示：
LED R1: Status=ON, Gray=X.X, Green=X.X, Red=X.X, Brightness=X.X%

计算：从日志开始到结束的持续时间
总ON时间：39.915秒 → GOOD
```

### 3. R2（红色LED 2）

#### 特征
- 位置：对应ROI 35（内部编号为2）
- 类型：红色LED
- 行为模式：开始时即为ON状态，通常有一次ON→OFF状态变化

#### 计算逻辑
1. **初始状态检测**：检查LED在日志开始时是否已经是ON状态
2. **状态变化处理**：
   - 如果开始时就是ON状态，从第一条记录开始计时
   - 处理状态变化事件（通常是ON→OFF）
   - 累加所有ON时间段
3. **边界处理**：正确处理开始时就是ON的情况
4. **总时间统计**：所有ON时间段的累加和

#### 实际案例
```
真实情况（基于实际日志）：
1. 11:06:29.045: 开始时即为ON状态
2. 11:06:35.725: ON -> OFF  (持续 6.680秒)
3. 之后保持OFF状态

总ON时间：6.680秒 → GOOD
```

## 算法流程

### 主要函数：`analyze_special_led_duration(led_name, roi_num, state_change_events)`

```python
def analyze_special_led_duration(led_name, roi_num, state_change_events):
    # 1. 提取该LED的状态变化事件
    led_events = extract_led_events(state_change_events, led_name, roi_num)
    
    if not led_events:
        # 2. 无状态变化 → 检查持续ON状态
        return calculate_continuous_on_time(led_name, roi_num)
    else:
        # 3. 有状态变化 → 计算ON时间段
        return calculate_on_periods(led_events)
```

### 关键步骤

1. **状态变化事件提取**
   - G系列：`LED G(\d+) state (\w+)->(\w+)`
   - R系列：`LED R(\d+) state (\w+)->(\w+)`

2. **持续ON状态检测**（适用于R1）
   - 搜索：`LED R1: Status=(\w+)`
   - 验证：所有记录都是ON状态
   - 计算：第一条到最后一条记录的时间差

3. **ON时间段计算**（适用于G33、R2）
   - 检查初始状态：如果开始时就是ON，从第一条记录开始计时
   - 遍历状态变化事件
   - OFF→ON：开始计时
   - ON→OFF：结束计时，累加时间
   - 最后仍ON：计算到日志结束

## 综合判断逻辑

### 单个LED判断
```python
is_good = total_duration > 1.0  # 超过1秒为好
```

### 综合状态判断
```python
all_special_leds_good = g33_result['is_good'] and r1_result['is_good'] and r2_result['is_good']
special_leds_status = "GOOD" if all_special_leds_good else "BAD"
```

### 通信输出
```python
# M13地址发送值
m13_value = 1 if special_leds_status == 'GOOD' else 3
```

## 实际运行结果示例

```
G33分析结果:
  总ON时间: 39.007秒
  ON次数: 2
  状态: 好 (需要>1秒)

R1分析结果:
  总ON时间: 39.915秒
  ON次数: 1
  状态: 好 (需要>1秒)

R2分析结果:
  总ON时间: 30.014秒
  ON次数: 3
  状态: 好 (需要>1秒)

特殊LED综合状态: GOOD
G33: OK, R1: OK, R2: OK
```

## 注意事项

1. **时间戳格式**：`YYYY-MM-DD HH:MM:SS.mmm`
2. **ROI映射**：
   - G33 → ROI 33
   - R1 → ROI 34（内部编号1）
   - R2 → ROI 35（内部编号2）
3. **边界情况**：
   - 日志开始时LED已经是ON状态
   - 日志结束时LED仍然是ON状态
   - 无状态变化但持续ON的情况
4. **容错处理**：
   - 时间戳解析失败时跳过该记录
   - 状态变化事件不完整时的处理
   - 计算时间差为负数时的处理

## 相关文件

- `analyze_led_log.py`：主分析脚本
- `analyze_special_leds_only.py`：专门分析特殊LED的脚本
- `test_35_points_analysis.py`：测试脚本
- `led_digit_detection.log`：原始日志文件
