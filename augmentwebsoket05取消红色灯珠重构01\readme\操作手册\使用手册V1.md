# LED数码管视觉检测系统 - 使用手册 V1.0

## 📋 系统概述

本系统是一个基于OpenCV的LED灯珠和数码管视觉检测工具，具备**ROI基准动态调整**功能，能够自动适应产品摆放位置的微小偏差，大幅提高检测稳定性。

### 🎯 主要功能
- **LED灯珠检测**：支持绿色和红色LED的亮灭状态检测
- **数码管检测**：7段数码管字符识别
- **基准点对齐**：自动校正产品位置偏差（±20像素容忍度）
- **实时监控**：连续检测和状态显示
- **配置管理**：保存/加载检测配置

---

## 🚀 快速开始

### 1. 启动系统
```bash
python main.py
```

### 2. 系统工作模式
系统有三个主要工作模式：
- **摄像头设置模式**：调整摄像头参数
- **校准模式**：配置检测区域和参数
- **检测模式**：实时检测和分析

---

## 🎥 摄像头设置

### 进入设置模式
启动后系统默认进入摄像头设置模式，或在任意模式下按 `C` 键。

### 基本操作
- **分辨率调整**：`R` 键切换分辨率（640x480, 1280x720, 1920x1080）
- **曝光调整**：`E`/`Shift+E` 增加/减少曝光值
- **亮度调整**：`B`/`Shift+B` 增加/减少亮度值
- **进入校准**：`Enter` 键进入校准模式

### 💡 设置建议
- 选择合适的分辨率（推荐1280x720）
- 调整曝光确保图像清晰不过曝
- 调整亮度使LED和数码管对比度明显

---

## ⚙️ 校准模式详解

校准模式是系统的核心，包含**基准点校准**、**LED校准**和**数码管校准**三个部分。

### 🎯 基准点校准（重要新功能）

基准点校准是ROI动态调整的基础，**强烈推荐使用**。

#### 进入基准点校准
在校准开始界面按 `B` 键进入基准点选择。

#### 选择基准点的要求
选择产品上的**两个明显特征点**作为位置参考：

✅ **推荐选择的特征点**：
- 螺丝孔（圆形，对比度高）
- 产品标记或印刷文字
- 明显的边角或几何形状
- 固定的机械结构

❌ **避免选择的区域**：
- 平坦无特征的表面
- 光照不均匀的区域
- 可能变化的部分（如LED本身）
- 图像边缘附近

#### 操作步骤
1. **选择第一个基准点**
   - 用鼠标点击产品上的明显特征点
   - 系统会显示黄色圆圈和模板边框
   - 看到"✓ 基准点 1 已选择"提示

2. **选择第二个基准点**
   - 选择与第一个点有一定距离的另一个特征点
   - 建议两点距离 > 50像素，< 500像素
   - 看到"✓ 基准点 2 已选择"提示

3. **确认或重选**
   - `Enter`：确认基准点，继续LED校准
   - `R`：重新选择基准点
   - `Esc`：跳过基准点功能（使用固定坐标模式）

#### 🔍 基准点质量指标
系统会自动评估基准点质量：
- **对比度检查**：确保特征点足够清晰
- **距离验证**：确保两点距离合理
- **模板完整性**：确保模板在图像边界内

### 🔴 LED校准

#### 进入LED校准
在校准开始界面按 `L` 键，或完成基准点校准后自动进入。

#### 操作步骤
1. **选择LED ROI**
   - 用鼠标拖拽框选每个LED区域
   - 按 `Enter` 确认当前ROI
   - 按 `Backspace` 删除当前ROI重选

2. **采集关闭状态样本**
   - 确保所有LED处于关闭状态
   - 按 `Space` 键采集样本（建议采集5-10个）
   - 按 `Enter` 完成关闭状态采集

3. **采集开启状态样本**
   - 确保所有LED处于开启状态
   - 按 `Space` 键采集样本（建议采集5-10个）
   - 按 `Enter` 完成开启状态采集

4. **阈值分析**
   - 系统自动计算最佳检测阈值
   - 按 `Enter` 确认阈值

### 🔢 数码管校准

#### 进入数码管校准
在校准开始界面按 `D` 键。

#### 操作步骤
1. **显示"88"**
   - 让数码管显示"88"（所有段点亮）
   - 按 `Space` 键确认

2. **选择数码管整体ROI**
   - 依次框选每个数码管的整体区域
   - 按 `Enter` 确认每个ROI

3. **选择段ROI**
   - 为每个数码管的7个段（a,b,c,d,e,f,g）分别选择ROI
   - 按照系统提示依次选择
   - 按 `Enter` 确认每个段ROI

4. **采集背景样本**
   - 让数码管显示空白或"00"
   - 按 `Space` 键采集背景样本

5. **调整亮度阈值**
   - 使用 `+`/`-` 键调整阈值
   - 观察检测结果，确保准确识别
   - 按 `Enter` 确认阈值

### 💾 保存配置
完成所有校准后：
- 按 `S` 键保存配置到文件
- 配置包含所有ROI坐标、阈值和基准点数据

---

## 🔍 检测模式

### 进入检测模式
- 校准完成后按 `Enter` 键
- 或在任意模式下按 `D` 键

### 🎯 基准点对齐工作原理

在检测模式下，系统会：

1. **自动搜索基准点**
   - 在每一帧中搜索之前校准的基准点位置
   - 使用模板匹配算法定位基准点

2. **计算位置偏移**
   - 比较当前基准点位置与校准时的原始位置
   - 计算X、Y方向的偏移量

3. **动态调整ROI**
   - 根据偏移量同步调整所有LED和数码管ROI
   - 确保ROI始终对准正确的检测区域

4. **状态显示**
   - 在界面上显示对齐状态：
     - `基准点对齐: ✓` - 对齐成功
     - `基准点对齐: 搜索中(N)` - 正在搜索，N为失败次数
     - `基准点对齐: 失败(N)` - 连续失败N次
     - `基准点对齐: 禁用` - 未启用基准点功能

### 检测结果显示
- **LED状态**：绿色圆圈（开启）/ 红色圆圈（关闭）
- **数码管识别**：显示识别的字符和置信度
- **实时统计**：检测帧率、状态变化等

### 快捷键操作
- `Esc`：退出检测模式
- `C`：进入摄像头设置
- `L`：清空日志文件
- `A`：启动LED周期分析（如果可用）

---

## 🛠️ 故障排除

### 基准点对齐问题

#### 问题：基准点对齐连续失败
**可能原因**：
- 光照条件变化
- 产品位置偏移过大
- 基准点被遮挡
- 基准点选择不当

**解决方案**：
1. 检查光照是否稳定
2. 调整产品位置回到大致原位
3. 重新校准基准点，选择更明显的特征
4. 降低匹配阈值（在配置中调整）

#### 问题：基准点对齐不准确
**可能原因**：
- 基准点距离太近
- 基准点对比度不足
- 模板质量差

**解决方案**：
1. 重新选择距离更远的基准点（>50像素）
2. 选择对比度更高的特征点
3. 改善光照条件

### LED检测问题

#### 问题：LED检测不准确
**解决方案**：
1. 检查ROI是否对准LED中心
2. 重新采集开启/关闭状态样本
3. 调整检测阈值
4. 确保基准点对齐正常工作

### 数码管检测问题

#### 问题：数码管识别错误
**解决方案**：
1. 检查段ROI是否准确覆盖每个段
2. 调整亮度阈值
3. 确保"88"校准时所有段都正确点亮
4. 检查基准点对齐状态

---

## 📁 配置文件管理

### 配置文件位置
- 主配置文件：`combined_config.json`
- 日志文件：`led_digit_detection.log`

### 配置文件内容
配置文件包含：
- 摄像头设置（分辨率、曝光、亮度）
- LED设置（ROI坐标、阈值、样本数据）
- 数码管设置（ROI坐标、段ROI、阈值）
- **基准点数据**（坐标、模板图像、对齐参数）
- **原始ROI坐标**（用于偏移计算）

### 备份和恢复
- **备份**：复制`combined_config.json`文件
- **恢复**：替换配置文件并重启系统
- **重置**：删除配置文件，系统将重新进入校准模式

---

## 💡 最佳实践

### 基准点选择建议
1. **选择固定不变的特征**：避免选择可能移动或变化的部分
2. **确保良好对比度**：选择与背景有明显区别的特征
3. **合理的距离分布**：两个基准点距离适中，覆盖检测区域
4. **避免边缘区域**：不要选择太靠近图像边缘的点

### 校准环境要求
1. **稳定光照**：避免强烈的光照变化
2. **固定摄像头**：确保摄像头位置稳定
3. **清洁镜头**：保持摄像头镜头清洁
4. **减少振动**：避免设备振动影响图像质量

### 检测精度优化
1. **定期重新校准**：环境变化时重新校准
2. **监控对齐状态**：注意基准点对齐状态指示
3. **调整匹配阈值**：根据实际情况微调参数
4. **保持环境一致**：尽量保持检测环境的一致性

---

## 📞 技术支持

### 系统要求
- Python 3.7+
- OpenCV 4.0+
- NumPy
- 支持USB摄像头的计算机

### 常见问题
如遇到问题，请检查：
1. 摄像头连接是否正常
2. 配置文件是否完整
3. 光照条件是否稳定
4. 基准点对齐状态是否正常

### 版本信息
- 文档版本：V1.0
- 系统版本：ROI基准动态调整v2.0
- 更新日期：2025-08-07

---

**🎉 恭喜！您已掌握LED数码管视觉检测系统的使用方法。基准点动态调整功能将大大提高您的检测稳定性和准确性！**
