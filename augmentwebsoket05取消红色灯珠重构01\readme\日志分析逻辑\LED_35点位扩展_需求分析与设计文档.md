# LED 35点位扩展项目 - 需求分析与设计文档

## 📋 项目概述

### 项目目标
将LED检测系统从16个单点顺序点亮模式升级为35个点位的复合检测模式：
- **32个点位**：G1-G32配对点亮模式（16对LED）
- **3个点位**：G33、R1、R2独立监控模式

### 版本信息
- **原版本**：16点位单点顺序模式
- **目标版本**：35点位复合检测模式
- **修改日期**：2025-08-04
- **修改文件**：analyze_led_log.py, ui_handler.py

## 🔄 需求变更对比

### 原有逻辑（16点位）
```
点位范围：ROI 1-16 (G1-G16)
点亮模式：单点顺序点亮
完美周期：ROI 1→2→3→...→16
独占性：每个时刻只有一个ROI为ON
验证规则：严格顺序 + 单点独占
输出地址：M10 (完美周期数)
```

### 新逻辑（35点位）
```
点位范围：ROI 1-35 (G1-G35, R1-R2)
点亮模式：配对点亮 + 独立监控
完美周期：16步配对点亮 (G1-G32)
独立监控：G33, R1, R2时间分析
输出地址：M10 (完美周期数) + M13 (独立监控结果)
```

## 🎯 核心逻辑设计

### 1. 配对关系定义
```python
# 配对映射关系
PAIR_MAPPING = {
    1: 17, 2: 18, 3: 19, 4: 20, 5: 21, 6: 22, 7: 23, 8: 24,
    9: 25, 10: 26, 11: 27, 12: 28, 13: 29, 14: 30, 15: 31, 16: 32,
    17: 1, 18: 2, 19: 3, 20: 4, 21: 5, 22: 6, 23: 7, 24: 8,
    25: 9, 26: 10, 27: 11, 28: 12, 29: 13, 30: 14, 31: 15, 32: 16
}

# 序列映射（G17-G32 → G1-G16）
def map_roi_to_sequence(roi_num):
    if 1 <= roi_num <= 16:
        return roi_num
    elif 17 <= roi_num <= 32:
        return roi_num - 16
    else:
        return None  # G33, R1, R2不参与序列
```

### 2. 完美周期验证算法
```python
def validate_perfect_cycle_step(current_roi, interval_statuses):
    """
    验证单步配对点亮是否完美
    
    Args:
        current_roi: 当前brightest LED的ROI编号
        interval_statuses: 时间区间内的所有状态记录
    
    Returns:
        bool: 是否为完美步骤
    """
    # 1. 确定配对ROI
    paired_roi = PAIR_MAPPING.get(current_roi)
    if not paired_roi:
        return False
    
    # 2. 检查配对ROI是否同时ON
    paired_on = any(status == "ON" and roi == paired_roi 
                   for _, roi, status in interval_statuses)
    
    # 3. 检查其他30个ROI是否全部OFF
    other_rois_off = all(status == "OFF" or roi in [current_roi, paired_roi]
                        for _, roi, status in interval_statuses 
                        if 1 <= roi <= 32)
    
    return paired_on and other_rois_off
```

### 3. 时间累计分析算法
```python
def analyze_duration_for_special_leds(status_events):
    """
    分析G33, R1, R2的ON状态持续时间
    
    Args:
        status_events: 所有状态变化事件
    
    Returns:
        dict: {led_name: total_on_duration}
    """
    special_leds = {'G33': 33, 'R1': 34, 'R2': 35}  # ROI映射
    results = {}
    
    for led_name, roi_num in special_leds.items():
        on_periods = []
        current_on_start = None
        
        # 提取该LED的状态变化
        led_events = [(ts, status) for ts, roi, status in status_events 
                     if roi == roi_num]
        
        # 计算ON状态持续时间
        for timestamp, status in led_events:
            if status == "ON" and current_on_start is None:
                current_on_start = timestamp
            elif status == "OFF" and current_on_start is not None:
                duration = calculate_time_diff(current_on_start, timestamp)
                on_periods.append(duration)
                current_on_start = None
        
        # 累计总时间
        total_duration = sum(on_periods)
        results[led_name] = {
            'total_duration': total_duration,
            'is_good': total_duration > 1.0,  # 超过1秒为好
            'on_periods': on_periods
        }
    
    return results
```

## 📊 数据流程设计

### 输入数据
```
日志文件：led_digit_detection.log
包含内容：
- G1-G35状态变化事件
- R1-R2状态变化事件
- 时间戳信息
- brightest LED标记
```

### 处理流程
```
1. 日志解析
   ├── 提取brightest LED事件 (G1-G32)
   ├── 提取所有状态变化事件
   └── 提取时间戳信息

2. 配对周期分析
   ├── 序列映射 (G17-G32 → G1-G16)
   ├── 配对验证 (每步检查配对LED同时ON)
   ├── 独占性验证 (其他30个LED必须OFF)
   └── 完美周期计数

3. 独立LED监控
   ├── G33时间累计分析
   ├── R1时间累计分析
   └── R2时间累计分析

4. 结果输出
   ├── 完美周期数 → M10地址
   └── 独立监控结果 → M13地址
```

### 输出数据
```
标准输出：
- Perfect Cycles Found: {count}
- G33 Duration Analysis: {result}
- R1 Duration Analysis: {result}  
- R2 Duration Analysis: {result}
- Special LEDs Status: {good/bad}

M区地址：
- M10: 完美周期结果 (0个→发送3, >0个→发送1)
- M13: 独立监控结果 (都好→发送1, 有问题→发送3)
```

## 🔧 实现计划

### 阶段1：核心逻辑修改
1. 扩展正则表达式模式
2. 实现配对验证算法
3. 实现序列映射逻辑
4. 实现时间累计分析

### 阶段2：通信逻辑更新
1. 修改结果解析逻辑
2. 添加M13地址发送
3. 更新状态机流程

### 阶段3：测试验证
1. 使用现有日志测试
2. 验证配对逻辑正确性
3. 验证时间分析准确性
4. 验证通信功能

## 📝 风险评估

### 技术风险
- 配对验证逻辑复杂性
- 时间计算精度问题
- 大日志文件性能影响

### 兼容性风险
- 现有系统集成问题
- 日志格式变化适应
- 网络通信稳定性

### 缓解措施
- 详细单元测试
- 渐进式部署
- 完整回滚方案
